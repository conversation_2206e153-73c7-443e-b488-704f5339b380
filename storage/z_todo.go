package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var todoDao = "todo_dao"

// GetID implements the GetID function for Entity Interface
func (impl *Todo) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *Todo) SetID(ID string) {
	// replace the logic to populate unique ID for Todo
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *Todo) NewEntity() data.Entity {
	return &Todo{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *Todo) GetTableName() string {
	return "todo"
}

// ITodoDAO is the dao interface for Todo
//go:generate mockery --name ITodoDAO --inpackage --case=underscore
type ITodoDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*Todo, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Todo, error)

	// Save ...
	Save(ctx context.Context, newData *Todo) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*Todo) error

	// Update ...
	Update(ctx context.Context, newData *Todo) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *Todo, newData *Todo) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*Todo, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Todo, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *Todo) error
}

// TodoDAO ...
type TodoDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewTodoDAO creates a data access object for Todo
func NewTodoDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *TodoDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &TodoDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &Todo{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *TodoDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*Todo, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(todoDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(todoDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*Todo), err
}

// LoadByIDOnSlave ...
func (dao *TodoDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Todo, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(todoDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(todoDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*Todo), err
}

// Save ...
func (dao *TodoDAO) Save(ctx context.Context, entity *Todo) error {
	methodName := "save"
	defer dao.StatsD.Duration(todoDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(todoDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *TodoDAO) SaveBatch(ctx context.Context, entities []*Todo) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(todoDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(todoDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *TodoDAO) Update(ctx context.Context, data *Todo) error {
	methodName := "update"
	defer dao.StatsD.Duration(todoDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(todoDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *TodoDAO) UpdateEntity(ctx context.Context, preData *Todo, newData *Todo) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(todoDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(todoDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *TodoDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*Todo, error) {
	methodName := "find"
	defer dao.StatsD.Duration(todoDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(todoDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*Todo, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Todo))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *TodoDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Todo, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(todoDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(todoDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*Todo, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Todo))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *TodoDAO) Upsert(ctx context.Context, data *Todo) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(todoDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(todoDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}
