// Package dto ...
package dto

import "time"

// CreateAccountStreamMessage defines structure for account created stream
type CreateAccountStreamMessage struct {
	AccountID           string
	Status              string
	ProductID           string
	ProductVersionID    string
	Name                string
	PermittedCurrencies []string
	StakeHolderIDs      []string
	OpeningTimestamp    *time.Time
	ClosingTimestamp    *time.Time
	InstanceParams      map[string]string
	DerivedParams       map[string]string
	EventID             string
	EventTimestamp      time.Time
	Details             map[string]string
}
