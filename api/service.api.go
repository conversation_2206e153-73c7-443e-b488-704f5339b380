// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package api

import (
	context "context"
	time "time"
)

// Request is an example request.
type Request struct {
	Value string `json:"value,omitempty"`
}

// Response is an example response.
type Response struct {
	Value string `json:"value,omitempty"`
}

// Template is generic template details meant to be compatible with Hedwig API.
type Template struct {
	ID       string            `json:"id,omitempty"`
	Params   map[string]string `json:"params,omitempty"`
	Language string            `json:"language,omitempty"`
}

// SmsRequest is meant to be compatible with Hedwig API defined at: https://wiki.grab.com/display/FAT/SMS+API.
type SmsRequest struct {
	RecipientID string    `json:"recipientID,omitempty"`
	Template    *Template `json:"template,omitempty"`
}

// SmsResponse is meant to be compatible with Hedwig API defined at: https://wiki.grab.com/display/FAT/SMS+API.
type SmsResponse struct {
	MessageID       string `json:"messageID,omitempty"`
	VendorInfo      string `json:"vendorInfo,omitempty"`
	VendorMessageID string `json:"vendorMessageID,omitempty"`
}

type RiskCheckRequest struct {
	UserAuthMechsOnAccount []string           `json:"userAuthMechsOnAccount,omitempty"`
	OriginalRequest        *OriginalRequest   `json:"originalRequest,omitempty"`
	SessionData            *SessionData       `json:"sessionData,omitempty"`
	MfaSessionHistory      []MfaChallengeInfo `json:"mfaSessionHistory,omitempty"`
}

type MfaChallengeInfo struct {
	EpochTime string    `json:"epochTime,omitempty"`
	MfaMech   string    `json:"mfaMech,omitempty"`
	Endpoint  *Endpoint `json:"endpoint,omitempty"`
}

type Endpoint struct {
	Path       string `json:"path,omitempty"`
	HttpMethod string `json:"httpMethod,omitempty"`
}

type OriginalRequest struct {
	Path        string `json:"path,omitempty"`
	HttpMethod  string `json:"httpMethod,omitempty"`
	ContentType string `json:"contentType,omitempty"`
	Payload     string `json:"payload,omitempty"`
}

type SessionData struct {
	SerivceID     string `json:"serivceID,omitempty"`
	ServiceUserID int64  `json:"serviceUserID,omitempty"`
	SafeID        string `json:"safeID,omitempty"`
	Jti           string `json:"jti,omitempty"`
}

type RiskCheckResponse struct {
	AppStatusCode int64                  `json:"appStatusCode,omitempty"`
	AppStatusDesc string                 `json:"appStatusDesc,omitempty"`
	Data          *RiskCheckResponseData `json:"data,omitempty"`
}

type RiskCheckResponseData struct {
	TransactionID string `json:"transactionID,omitempty"`
	MfaRequired   bool   `json:"mfaRequired,omitempty"`
	MfaMech       string `json:"mfaMech,omitempty"`
	AuxInfo       string `json:"auxInfo,omitempty"`
	UserSafeID    string `json:"userSafeID,omitempty"`
}

type PartnerTxnRequest struct {
	Partner string   `json:"partner,omitempty"`
	Payload *Payload `json:"payload,omitempty"`
	TxnType string   `json:"txnType,omitempty"`
}

type Payload struct {
	CustomerID string    `json:"customerID,omitempty"`
	IpAddress  string    `json:"ipAddress,omitempty"`
	DeviceID   string    `json:"deviceID,omitempty"`
	Category   string    `json:"category,omitempty"`
	CreatedAt  time.Time `json:"createdAt,omitempty"`
}

type PartnerTxnResponse struct {
	Status       int64  `json:"status,omitempty"`
	Action       int64  `json:"action,omitempty"`
	Reason       string `json:"reason,omitempty"`
	ExtraMessage string `json:"extraMessage,omitempty"`
}

type TestGetRequest struct {
	Abc   string `json:"abc,omitempty"`
	Flag  bool   `json:"flag,omitempty"`
	Count int32  `json:"count,omitempty"`
}

type TestGetResponse struct {
	Abc   string `json:"abc,omitempty"`
	Flag  bool   `json:"flag,omitempty"`
	Count int32  `json:"count,omitempty"`
}

// EmailAttachment is email attachment details meant to be compatible with Hedwig API.
type EmailAttachment struct {
	Filename string `json:"filename,omitempty"`
	File     []byte `json:"file,omitempty"`
	Url      string `json:"url,omitempty"`
}

// EmailRequest is meant to be compatible with Hedwig API defined at https://wiki.grab.com/display/FAT/Email+API.
type EmailRequest struct {
	RecipientID  string            `json:"recipientID,omitempty"`
	Template     *Template         `json:"template,omitempty"`
	Category     string            `json:"category,omitempty"`
	ReplyToEmail string            `json:"replyToEmail,omitempty"`
	Attachments  []EmailAttachment `json:"attachments,omitempty"`
}

// EmailResponse is meant to be compatible with Hedwig API defined at https://wiki.grab.com/display/FAT/Email+API.
type EmailResponse struct {
	MessageID string `json:"messageID,omitempty"`
}

// PushRequest is meant to be compatible with Hedwig API defined at https://wiki.grab.com/pages/viewpage.action?pageId=250952827.
type PushRequest struct {
	RecipientID   string    `json:"recipientID,omitempty"`
	RecipientType string    `json:"recipientType,omitempty"`
	Template      *Template `json:"template,omitempty"`
}

// PushResponse is meant to be compatible with Hedwig API defined at https://wiki.grab.com/pages/viewpage.action?pageId=250952827.
type PushResponse struct {
	MessageID string `json:"messageID,omitempty"`
	Target    string `json:"target,omitempty"`
	Reason    string `json:"reason,omitempty"`
	Message   string `json:"message,omitempty"`
}

type RetrieveOTPRequest struct {
	MsgID      string `json:"msgID,omitempty"`
	Identifier string `json:"identifier,omitempty"`
	Channel    string `json:"channel,omitempty"`
}

type RetrieveOTPResponse struct {
	Status       string `json:"status,omitempty"`
	StatusReason string `json:"statusReason,omitempty"`
	Proof        string `json:"proof,omitempty"`
}

type GrabMexRequest struct {
	Id      string `json:"id,omitempty"`
	Country string `json:"country,omitempty"`
	Page    string `json:"page,omitempty"`
}

type GrabMexResponse struct {
	Last_1MonthTransCount   int32   `json:"last1MonthTransCount,omitempty"`
	Last_3MonthsTransCount  int32   `json:"last3MonthsTransCount,omitempty"`
	Last_6MonthsTransCount  int32   `json:"last6MonthsTransCount,omitempty"`
	Last_12MonthsTransCount int32   `json:"last12MonthsTransCount,omitempty"`
	GrabActiveFlag          string  `json:"grabActiveFlag,omitempty"`
	GrabBlackListFlag       string  `json:"grabBlackListFlag,omitempty"`
	Stores                  []Store `json:"stores,omitempty"`
	HasMore                 bool    `json:"hasMore,omitempty"`
}

type Address struct {
	AddressType string `json:"addressType,omitempty"`
	Block       string `json:"block,omitempty"`
	Street      string `json:"street,omitempty"`
	Unit        string `json:"unit,omitempty"`
	City        string `json:"city,omitempty"`
	State       string `json:"state,omitempty"`
	Country     string `json:"country,omitempty"`
	PostalCode  string `json:"postalCode,omitempty"`
}

type Store struct {
	StoreTradingName         string   `json:"storeTradingName,omitempty"`
	BusinessOperatingAddress string   `json:"businessOperatingAddress,omitempty"`
	BusinessOperationAddress *Address `json:"businessOperationAddress,omitempty"`
}

// Foobar <TODO: add a short description here>
type Foobar interface {
	Example(ctx context.Context, req *Request) (*Response, error)
	// Sms publishes a SMS to specified phone number using pre-defined template.
	Sms(ctx context.Context, req *SmsRequest) (*SmsResponse, error)
	// Authentication Check.
	AuthCheck(ctx context.Context, req *Request) (*Response, error)
	RiskCheck(ctx context.Context, req *RiskCheckRequest) (*RiskCheckResponse, error)
	PartnerTxnValidation(ctx context.Context, req *PartnerTxnRequest) (*PartnerTxnResponse, error)
	TestGet(ctx context.Context, req *TestGetRequest) (*TestGetResponse, error)
	// Email sends a email to specified address using pre-defined template.
	Email(ctx context.Context, req *EmailRequest) (*EmailResponse, error)
	// Push sends a push notification to specified phone using pre-defined template.
	Push(ctx context.Context, req *PushRequest) (*PushResponse, error)
	// Push sends a push notification to specified phone using pre-defined template.
	CreateLoc(ctx context.Context, req *Request) (*Response, error)
	RetrieveOTP(ctx context.Context, req *RetrieveOTPRequest) (*RetrieveOTPResponse, error)
	// Returns response code depending on config. Use to test canary rollout
	TestCanary(ctx context.Context) error
	GrabMex(ctx context.Context, req *GrabMexRequest) (*GrabMexResponse, error)
}
