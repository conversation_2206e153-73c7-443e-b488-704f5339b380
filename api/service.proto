syntax = "proto3";

package foobar;

option go_package = "gitlab.myteksi.net/dbmy/foobar/api";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";


// Request is an example request.
message Request {
    string value = 1;
}

// Response is an example response.
message Response {
    string value = 1;
}

// Template is generic template details meant to be compatible with Hedwig API.
message Template {
    string ID = 1 [ json_name="id" ];
    map<string, string> params = 2;
    string language = 3;
}

// SmsRequest is meant to be compatible with Hedwig API defined at: https://wiki.grab.com/display/FAT/SMS+API.
message SmsRequest {
    string recipientID = 1;
    Template template = 3;
}

// SmsResponse is meant to be compatible with Hedwig API defined at: https://wiki.grab.com/display/FAT/SMS+API.
message SmsResponse {
    string messageID = 1;
    string vendorInfo = 2;
    string vendorMessageID = 3;
}

message RiskCheckRequest {
    repeated string userAuthMechsOnAccount = 1;
    OriginalRequest originalRequest = 2;
    SessionData sessionData = 3;
    repeated MfaChallengeInfo mfaSessionHistory = 4;
}

message MfaChallengeInfo {
    string epochTime = 1;
    string mfaMech = 2;
    Endpoint endpoint = 3;
}

message Endpoint {
    string path = 1;
    string httpMethod = 2;
}

message OriginalRequest {
    string path = 1;
    string httpMethod = 2;
    string contentType = 3;
    string payload = 4;
}

message SessionData {
    string serivceID = 1;
    int64 serviceUserID = 2;
    string safeID = 3;
    string jti = 4;
}

message RiskCheckResponse {
    int64 appStatusCode = 1;
    string appStatusDesc = 2;
    RiskCheckResponseData data = 3;
}

message RiskCheckResponseData {
    string transactionID = 1;
    bool mfaRequired = 2;
    string mfaMech = 3;
    string auxInfo = 4;
    string userSafeID = 5;
}

message PartnerTxnRequest {
    string partner = 1;
    Payload payload  = 2;
    string txnType = 3;

}

message Payload {
    string customerID = 1;
    string ipAddress = 2;
    string deviceID = 3;
    string category = 4;
    google.protobuf.Timestamp createdAt = 5;
}

message PartnerTxnResponse {
    int64 status = 1;
    int64 action = 2;
    string reason = 3;
    string extraMessage = 4;
}

message TestGetRequest {
    string abc = 1;
    bool flag = 2;
    int32 count = 3;
}

message TestGetResponse {
    string abc = 1;
    bool flag = 2;
    int32 count = 3;
}


// EmailAttachment is email attachment details meant to be compatible with Hedwig API.
message EmailAttachment {
    string filename = 1;
    bytes file = 2;
    string url = 3;
}

// EmailRequest is meant to be compatible with Hedwig API defined at https://wiki.grab.com/display/FAT/Email+API.
message EmailRequest {
    string recipientID = 1;
    Template template = 3;
    string category = 4;
    string replyToEmail = 5;
    repeated EmailAttachment attachments = 7;
}

// EmailResponse is meant to be compatible with Hedwig API defined at https://wiki.grab.com/display/FAT/Email+API.
message EmailResponse {
    string messageID = 1;
}


// PushRequest is meant to be compatible with Hedwig API defined at https://wiki.grab.com/pages/viewpage.action?pageId=250952827.
message PushRequest {
    string recipientID = 1;
    string recipientType = 2;
    Template template = 3;
}

// PushResponse is meant to be compatible with Hedwig API defined at https://wiki.grab.com/pages/viewpage.action?pageId=250952827.
message PushResponse {
    string messageID = 1;
    string target = 2;
    string reason = 3;
    string message = 4;
}

message RetrieveOTPRequest{
    string msgID = 1;
    string identifier = 2;
    string channel = 3;
}

message RetrieveOTPResponse{
    string status = 1;
    string statusReason = 2;
    string proof = 3;
}

message GrabMexRequest{
    string id = 1;
    string country = 2;
    string page =3;
}

message GrabMexResponse { // Replace with a meaningful name for your message
    int32 last_1_month_trans_count = 1;
    int32 last_3_months_trans_count = 2;
    int32 last_6_months_trans_count = 3;
    int32 last_12_months_trans_count = 4;
    string grab_active_flag = 5;       // Assuming 'Status' can be represented as an int
    string grab_black_list_flag = 6;   // Assuming 'GrabBlackListStatus' can be represented as an int
    repeated Store stores = 7;
    bool has_more = 8;
}

message Address {
    string address_type = 1;
    string block = 2;
    string street = 3;
    string unit = 4;
    string city = 5;
    string state = 6;
    string country = 7;
    string postal_code = 8;
}

message Store {
    string store_trading_name = 1;
    string business_operating_address = 2;
    Address business_operation_address = 3;
}

// Foobar <TODO: add a short description here>
service Foobar {
    rpc Example(Request) returns (Response) {
        option (google.api.http) = {
            post: "/api/v1/example",
            body: "*",
        };
    }
    // Sms publishes a SMS to specified phone number using pre-defined template.
    rpc Sms(SmsRequest) returns (SmsResponse) {
        option (google.api.http) = {
            post: "/v1/sms",
            body: "*",
        };
    }
    // Authentication Check.
    rpc AuthCheck(Request) returns (Response) {
        option (google.api.http) = {
            get: "/v1/auth-check",
        };
    }

    rpc RiskCheck (RiskCheckRequest) returns (RiskCheckResponse) {
        option (google.api.http) = {
            post: "/v1/risk-check"
            body: "*"
        };
    }

    rpc PartnerTxnValidation (PartnerTxnRequest) returns (PartnerTxnResponse) {
        option (google.api.http) = {
            post: "/risk/api/v2/validate/partnertxn"
            body: "*"
        };
    }

    rpc TestGet (TestGetRequest) returns (TestGetResponse) {
        option (google.api.http) = {
            get: "/test/get"
        };
    }

    // Email sends a email to specified address using pre-defined template.
    rpc Email (EmailRequest) returns (EmailResponse) {
        option (google.api.http) = {
            post: "/v1/email"
            body: "*"
        };
    }

    // Push sends a push notification to specified phone using pre-defined template.
    rpc Push(PushRequest) returns (PushResponse) {
        option (google.api.http) = {
            post: "/hedwig/v1/push",
            body: "*",
        };
    }

    // Push sends a push notification to specified phone using pre-defined template.
    rpc CreateLoc(Request) returns (Response) {
        option (google.api.http) = {
            post: "/workflow/v1/create_loc",
            body: "*",
        };
    }

    rpc RetrieveOTP(RetrieveOTPRequest) returns (RetrieveOTPResponse) {
        option (google.api.http) = {
            get: "/v1/comms/otp",
            body: "*",
        };
    }

    // Returns response code depending on config. Use to test canary rollout
    rpc TestCanary(google.protobuf.Empty) returns (google.protobuf.Empty) {
        option (google.api.http) = {
            get: "/v1/canary",
            body: "*",
        };
    }
    rpc GrabMex(GrabMexRequest) returns (GrabMexResponse) {
        option (google.api.http) = {
            get: "/grabfood/partner/v1/bank/onboarding-mex-metadata",
            body: "*",
        };
    }
}
