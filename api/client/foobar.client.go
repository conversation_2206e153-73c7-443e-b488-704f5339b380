// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package client

import (
	bytes "bytes"
	context "context"
	_go "github.com/json-iterator/go"
	klient "gitlab.myteksi.net/dakota/klient"
	errorhandling "gitlab.myteksi.net/dakota/klient/errorhandling"
	initialize "gitlab.myteksi.net/dakota/klient/initialize"
	api "gitlab.myteksi.net/dbmy/foobar/api"
	http "net/http"
)

// FoobarClient makes calls to Foobar service.
type FoobarClient struct {
	machinery klient.RoundTripper
}

// MakeFoobarClient instantiates a new FoobarClient.
// Deprecated: Use NewFoobarClient instead
func MakeFoobarClient(initializer klient.Initializer) (*FoobarClient, error) {
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &FoobarClient{
		machinery: roundTripper,
	}, nil
}

// NewFoobarClient instantiates a new FoobarClient.
func NewFoobarClient(baseURL string, options ...klient.Option) (*FoobarClient, error) {
	initializer := initialize.New(baseURL, options...)
	roundTripper, err := initializer.Initialize()
	if err != nil {
		return nil, err
	}
	return &FoobarClient{
		machinery: roundTripper,
	}, nil
}

func (f *FoobarClient) Example(ctx context.Context, req *api.Request) (*api.Response, error) {
	reqShell := (*ExampleRequestShell)(req)
	resShell := &ExampleResponseShell{}
	clientCtx := klient.MakeContext(ctx, &exampleDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.Response)(resShell), err
}

// Sms publishes a SMS to specified phone number using pre-defined template.
func (f *FoobarClient) Sms(ctx context.Context, req *api.SmsRequest) (*api.SmsResponse, error) {
	reqShell := (*SmsRequestShell)(req)
	resShell := &SmsResponseShell{}
	clientCtx := klient.MakeContext(ctx, &smsDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.SmsResponse)(resShell), err
}

// Authentication Check.
func (f *FoobarClient) AuthCheck(ctx context.Context, req *api.Request) (*api.Response, error) {
	reqShell := (*AuthCheckRequestShell)(req)
	resShell := &AuthCheckResponseShell{}
	clientCtx := klient.MakeContext(ctx, &authCheckDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.Response)(resShell), err
}

func (f *FoobarClient) RiskCheck(ctx context.Context, req *api.RiskCheckRequest) (*api.RiskCheckResponse, error) {
	reqShell := (*RiskCheckRequestShell)(req)
	resShell := &RiskCheckResponseShell{}
	clientCtx := klient.MakeContext(ctx, &riskCheckDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.RiskCheckResponse)(resShell), err
}

func (f *FoobarClient) PartnerTxnValidation(ctx context.Context, req *api.PartnerTxnRequest) (*api.PartnerTxnResponse, error) {
	reqShell := (*PartnerTxnValidationRequestShell)(req)
	resShell := &PartnerTxnValidationResponseShell{}
	clientCtx := klient.MakeContext(ctx, &partnerTxnValidationDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.PartnerTxnResponse)(resShell), err
}

func (f *FoobarClient) TestGet(ctx context.Context, req *api.TestGetRequest) (*api.TestGetResponse, error) {
	reqShell := (*TestGetRequestShell)(req)
	resShell := &TestGetResponseShell{}
	clientCtx := klient.MakeContext(ctx, &testGetDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.TestGetResponse)(resShell), err
}

// Email sends a email to specified address using pre-defined template.
func (f *FoobarClient) Email(ctx context.Context, req *api.EmailRequest) (*api.EmailResponse, error) {
	reqShell := (*EmailRequestShell)(req)
	resShell := &EmailResponseShell{}
	clientCtx := klient.MakeContext(ctx, &emailDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.EmailResponse)(resShell), err
}

// Push sends a push notification to specified phone using pre-defined template.
func (f *FoobarClient) Push(ctx context.Context, req *api.PushRequest) (*api.PushResponse, error) {
	reqShell := (*PushRequestShell)(req)
	resShell := &PushResponseShell{}
	clientCtx := klient.MakeContext(ctx, &pushDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.PushResponse)(resShell), err
}

// Push sends a push notification to specified phone using pre-defined template.
func (f *FoobarClient) CreateLoc(ctx context.Context, req *api.Request) (*api.Response, error) {
	reqShell := (*CreateLocRequestShell)(req)
	resShell := &CreateLocResponseShell{}
	clientCtx := klient.MakeContext(ctx, &createLocDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.Response)(resShell), err
}

func (f *FoobarClient) RetrieveOTP(ctx context.Context, req *api.RetrieveOTPRequest) (*api.RetrieveOTPResponse, error) {
	reqShell := (*RetrieveOTPRequestShell)(req)
	resShell := &RetrieveOTPResponseShell{}
	clientCtx := klient.MakeContext(ctx, &retrieveOTPDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.RetrieveOTPResponse)(resShell), err
}

// Returns response code depending on config. Use to test canary rollout
func (f *FoobarClient) TestCanary(ctx context.Context) error {
	reqShell := (*TestCanaryRequestShell)(&struct{}{})
	resShell := &TestCanaryResponseShell{}
	clientCtx := klient.MakeContext(ctx, &testCanaryDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return err
}

func (f *FoobarClient) GrabMex(ctx context.Context, req *api.GrabMexRequest) (*api.GrabMexResponse, error) {
	reqShell := (*GrabMexRequestShell)(req)
	resShell := &GrabMexResponseShell{}
	clientCtx := klient.MakeContext(ctx, &grabMexDescriptor)
	err := f.machinery.RoundTrip(clientCtx, reqShell, resShell)
	return (*api.GrabMexResponse)(resShell), err
}

// ExampleRequestShell is a wrapper to make the object a klient.Request
type ExampleRequestShell api.Request

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (e *ExampleRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/api/v1/example"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(e)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// ExampleResponseShell is a wrapper to make the object a klient.Request
type ExampleResponseShell api.Response

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (e *ExampleResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(e)
}

// SmsRequestShell is a wrapper to make the object a klient.Request
type SmsRequestShell api.SmsRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (s *SmsRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/sms"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(s)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// SmsResponseShell is a wrapper to make the object a klient.Request
type SmsResponseShell api.SmsResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (s *SmsResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(s)
}

// AuthCheckRequestShell is a wrapper to make the object a klient.Request
type AuthCheckRequestShell api.Request

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (a *AuthCheckRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/auth-check"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// AuthCheckResponseShell is a wrapper to make the object a klient.Request
type AuthCheckResponseShell api.Response

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (a *AuthCheckResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(a)
}

// RiskCheckRequestShell is a wrapper to make the object a klient.Request
type RiskCheckRequestShell api.RiskCheckRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (r *RiskCheckRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/risk-check"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(r)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// RiskCheckResponseShell is a wrapper to make the object a klient.Request
type RiskCheckResponseShell api.RiskCheckResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (r *RiskCheckResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(r)
}

// PartnerTxnValidationRequestShell is a wrapper to make the object a klient.Request
type PartnerTxnValidationRequestShell api.PartnerTxnRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (p *PartnerTxnValidationRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/risk/api/v2/validate/partnertxn"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(p)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// PartnerTxnValidationResponseShell is a wrapper to make the object a klient.Request
type PartnerTxnValidationResponseShell api.PartnerTxnResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (p *PartnerTxnValidationResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(p)
}

// TestGetRequestShell is a wrapper to make the object a klient.Request
type TestGetRequestShell api.TestGetRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (t *TestGetRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/test/get"
	fullURL := baseURL + filledPath

	return http.NewRequest("GET", fullURL, nil)
}

// TestGetResponseShell is a wrapper to make the object a klient.Request
type TestGetResponseShell api.TestGetResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (t *TestGetResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(t)
}

// EmailRequestShell is a wrapper to make the object a klient.Request
type EmailRequestShell api.EmailRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (e *EmailRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/email"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(e)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// EmailResponseShell is a wrapper to make the object a klient.Request
type EmailResponseShell api.EmailResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (e *EmailResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(e)
}

// PushRequestShell is a wrapper to make the object a klient.Request
type PushRequestShell api.PushRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (p *PushRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/hedwig/v1/push"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(p)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// PushResponseShell is a wrapper to make the object a klient.Request
type PushResponseShell api.PushResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (p *PushResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(p)
}

// CreateLocRequestShell is a wrapper to make the object a klient.Request
type CreateLocRequestShell api.Request

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (c *CreateLocRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/workflow/v1/create_loc"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(c)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("POST", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// CreateLocResponseShell is a wrapper to make the object a klient.Request
type CreateLocResponseShell api.Response

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (c *CreateLocResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

// RetrieveOTPRequestShell is a wrapper to make the object a klient.Request
type RetrieveOTPRequestShell api.RetrieveOTPRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (r *RetrieveOTPRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/comms/otp"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(r)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// RetrieveOTPResponseShell is a wrapper to make the object a klient.Request
type RetrieveOTPResponseShell api.RetrieveOTPResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (r *RetrieveOTPResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(r)
}

// TestCanaryRequestShell is a wrapper to make the object a klient.Request
type TestCanaryRequestShell struct{}

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (t *TestCanaryRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/v1/canary"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(t)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// TestCanaryResponseShell is a wrapper to make the object a klient.Request
type TestCanaryResponseShell struct{}

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (t *TestCanaryResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return nil
}

// GrabMexRequestShell is a wrapper to make the object a klient.Request
type GrabMexRequestShell api.GrabMexRequest

// EncodeHTTPRequest encodes the request object into a *http.Request.
func (g *GrabMexRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	filledPath := "/grabfood/partner/v1/bank/onboarding-mex-metadata"
	fullURL := baseURL + filledPath

	jsonBytes, err := _go.Marshal(g)
	if err != nil {
		return nil, err
	}
	body := bytes.NewBuffer(jsonBytes)

	req, err := http.NewRequest("GET", fullURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	return req, nil
}

// GrabMexResponseShell is a wrapper to make the object a klient.Request
type GrabMexResponseShell api.GrabMexResponse

// DecodeHTTPResponse decodes a *http.Response into the response object.
func (g *GrabMexResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= 400 {
		return errorhandling.UnmarshalError(res)
	}

	return _go.NewDecoder(res.Body).Decode(g)
}

var exampleDescriptor = klient.EndpointDescriptor{
	Name:        "Example",
	Description: "",
	Method:      "POST",
	Path:        "/api/v1/example",
}

var smsDescriptor = klient.EndpointDescriptor{
	Name:        "Sms",
	Description: "Sms publishes a SMS to specified phone number using pre-defined template.",
	Method:      "POST",
	Path:        "/v1/sms",
}

var authCheckDescriptor = klient.EndpointDescriptor{
	Name:        "AuthCheck",
	Description: "Authentication Check.",
	Method:      "GET",
	Path:        "/v1/auth-check",
}

var riskCheckDescriptor = klient.EndpointDescriptor{
	Name:        "RiskCheck",
	Description: "",
	Method:      "POST",
	Path:        "/v1/risk-check",
}

var partnerTxnValidationDescriptor = klient.EndpointDescriptor{
	Name:        "PartnerTxnValidation",
	Description: "",
	Method:      "POST",
	Path:        "/risk/api/v2/validate/partnertxn",
}

var testGetDescriptor = klient.EndpointDescriptor{
	Name:        "TestGet",
	Description: "",
	Method:      "GET",
	Path:        "/test/get",
}

var emailDescriptor = klient.EndpointDescriptor{
	Name:        "Email",
	Description: "Email sends a email to specified address using pre-defined template.",
	Method:      "POST",
	Path:        "/v1/email",
}

var pushDescriptor = klient.EndpointDescriptor{
	Name:        "Push",
	Description: "Push sends a push notification to specified phone using pre-defined template.",
	Method:      "POST",
	Path:        "/hedwig/v1/push",
}

var createLocDescriptor = klient.EndpointDescriptor{
	Name:        "CreateLoc",
	Description: "Push sends a push notification to specified phone using pre-defined template.",
	Method:      "POST",
	Path:        "/workflow/v1/create_loc",
}

var retrieveOTPDescriptor = klient.EndpointDescriptor{
	Name:        "RetrieveOTP",
	Description: "",
	Method:      "GET",
	Path:        "/v1/comms/otp",
}

var testCanaryDescriptor = klient.EndpointDescriptor{
	Name:        "TestCanary",
	Description: "Returns response code depending on config. Use to test canary rollout",
	Method:      "GET",
	Path:        "/v1/canary",
}

var grabMexDescriptor = klient.EndpointDescriptor{
	Name:        "GrabMex",
	Description: "",
	Method:      "GET",
	Path:        "/grabfood/partner/v1/bank/onboarding-mex-metadata",
}
