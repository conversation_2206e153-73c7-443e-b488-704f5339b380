// Code generated by mockery v2.46.3. DO NOT EDIT.

package mocks

import (
	context "context"

	api "gitlab.myteksi.net/dbmy/foobar/api"

	mock "github.com/stretchr/testify/mock"
)

// Foobar is an autogenerated mock type for the Foobar type
type Foobar struct {
	mock.Mock
}

// AuthCheck provides a mock function with given fields: ctx, req
func (_m *Foobar) AuthCheck(ctx context.Context, req *api.Request) (*api.Response, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for AuthCheck")
	}

	var r0 *api.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.Request) (*api.Response, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.Request) *api.Response); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.Request) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateLoc provides a mock function with given fields: ctx, req
func (_m *Foobar) CreateLoc(ctx context.Context, req *api.Request) (*api.Response, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateLoc")
	}

	var r0 *api.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.Request) (*api.Response, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.Request) *api.Response); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.Request) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Email provides a mock function with given fields: ctx, req
func (_m *Foobar) Email(ctx context.Context, req *api.EmailRequest) (*api.EmailResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for Email")
	}

	var r0 *api.EmailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.EmailRequest) (*api.EmailResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.EmailRequest) *api.EmailResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.EmailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.EmailRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Example provides a mock function with given fields: ctx, req
func (_m *Foobar) Example(ctx context.Context, req *api.Request) (*api.Response, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for Example")
	}

	var r0 *api.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.Request) (*api.Response, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.Request) *api.Response); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.Request) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GrabMex provides a mock function with given fields: ctx, req
func (_m *Foobar) GrabMex(ctx context.Context, req *api.GrabMexRequest) (*api.GrabMexResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GrabMex")
	}

	var r0 *api.GrabMexResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.GrabMexRequest) (*api.GrabMexResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.GrabMexRequest) *api.GrabMexResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.GrabMexResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.GrabMexRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PartnerTxnValidation provides a mock function with given fields: ctx, req
func (_m *Foobar) PartnerTxnValidation(ctx context.Context, req *api.PartnerTxnRequest) (*api.PartnerTxnResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for PartnerTxnValidation")
	}

	var r0 *api.PartnerTxnResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.PartnerTxnRequest) (*api.PartnerTxnResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.PartnerTxnRequest) *api.PartnerTxnResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.PartnerTxnResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.PartnerTxnRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Push provides a mock function with given fields: ctx, req
func (_m *Foobar) Push(ctx context.Context, req *api.PushRequest) (*api.PushResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for Push")
	}

	var r0 *api.PushResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.PushRequest) (*api.PushResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.PushRequest) *api.PushResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.PushResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.PushRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RetrieveOTP provides a mock function with given fields: ctx, req
func (_m *Foobar) RetrieveOTP(ctx context.Context, req *api.RetrieveOTPRequest) (*api.RetrieveOTPResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveOTP")
	}

	var r0 *api.RetrieveOTPResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.RetrieveOTPRequest) (*api.RetrieveOTPResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.RetrieveOTPRequest) *api.RetrieveOTPResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.RetrieveOTPResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.RetrieveOTPRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RiskCheck provides a mock function with given fields: ctx, req
func (_m *Foobar) RiskCheck(ctx context.Context, req *api.RiskCheckRequest) (*api.RiskCheckResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for RiskCheck")
	}

	var r0 *api.RiskCheckResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.RiskCheckRequest) (*api.RiskCheckResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.RiskCheckRequest) *api.RiskCheckResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.RiskCheckResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.RiskCheckRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Sms provides a mock function with given fields: ctx, req
func (_m *Foobar) Sms(ctx context.Context, req *api.SmsRequest) (*api.SmsResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for Sms")
	}

	var r0 *api.SmsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.SmsRequest) (*api.SmsResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.SmsRequest) *api.SmsResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.SmsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.SmsRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TestCanary provides a mock function with given fields: ctx
func (_m *Foobar) TestCanary(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for TestCanary")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TestGet provides a mock function with given fields: ctx, req
func (_m *Foobar) TestGet(ctx context.Context, req *api.TestGetRequest) (*api.TestGetResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for TestGet")
	}

	var r0 *api.TestGetResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.TestGetRequest) (*api.TestGetResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.TestGetRequest) *api.TestGetResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.TestGetResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.TestGetRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewFoobar creates a new instance of Foobar. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFoobar(t interface {
	mock.TestingT
	Cleanup(func())
}) *Foobar {
	mock := &Foobar{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
