// Package template provides a singleton for loading and managing HTML templates.
package template

import (
	"bytes"
	"context"
	"fmt"
	html "html/template"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"sync"
	"text/template"

	"github.com/samber/lo"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

type config struct {
	Engine    EngineType
	Delimiter []string
}

type Executor interface {
	Execute(wr io.Writer, data any) error
	Name() string
}

// EngineType ...
type EngineType string

const (
	// EngineText ...
	EngineText EngineType = "text"
	// EngineHTML ...
	EngineHTML EngineType = "html"
)

var templateConfigs = map[string]config{
	"casa-interest-calculation.tpl.html": {Engine: EngineHTML, Delimiter: []string{"{%", "%}"}},
}

// Manager is a singleton that manages HTML templates.
type Manager struct {
	templates map[string]Executor
	mutex     sync.RWMutex
}

var (
	instance *Manager
	once     sync.Once
)

// GetInstance returns the singleton instance of the template manager.
func GetInstance() *Manager {
	once.Do(func() {
		instance = &Manager{
			templates: make(map[string]Executor),
		}
	})
	return instance
}

// LoadTemplates loads all HTML templates from the specified directory.
// It panics if there's a failure loading any template.
func (m *Manager) LoadTemplates(ctx context.Context, dir string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	err := filepath.Walk(dir, func(path string, info fs.FileInfo, err error) error {
		if err != nil {
			return fmt.Errorf("error accessing path %s: %w", path, err)
		}

		// Skip directories
		if info.IsDir() {
			return nil
		}

		// Only process HTML template files
		if filepath.Ext(path) != ".html" {
			return nil
		}

		// Read the template file
		content, err := os.ReadFile(path)
		if err != nil {
			return fmt.Errorf("error reading template file %s: %w", path, err)
		}
		templateName := filepath.Base(path)
		if lo.HasKey(m.templates, templateName) {
			slog.FromContext(ctx).Warn("template", fmt.Sprintf("template %s already exists, will be overridden", templateName))

		}
		conf := config{}

		if customConf, ok := templateConfigs[templateName]; ok {
			conf = customConf
		}
		// parse and register template into the map
		if conf.Engine == EngineHTML {
			tmpl := html.New(templateName)
			if len(conf.Delimiter) == 2 {
				tmpl = tmpl.Delims(conf.Delimiter[0], conf.Delimiter[1])
			}
			tmpl, tmplErr := tmpl.Parse(string(content))
			if tmplErr != nil {
				return fmt.Errorf("error parsing html template %s: %w", path, tmplErr)
			}
			m.setTemplate(templateName, tmpl)
		} else {
			tmpl, tmplErr := template.New(templateName).Parse(string(content))
			if tmplErr != nil {
				return fmt.Errorf("error parsing template %s: %w", path, tmplErr)
			}
			m.setTemplate(templateName, tmpl)
		}

		return nil
	})

	if err != nil {
		panic(fmt.Sprintf("failed to load templates: %v", err))
	}
}

// GetTemplate returns a template by name.
func (m *Manager) GetTemplate(name string) (Executor, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	tmpl, ok := m.templates[name]
	if !ok {
		return nil, fmt.Errorf("template %s not found", name)
	}
	return tmpl, nil
}

// setTemplate sets a template by name.
func (m *Manager) setTemplate(name string, tmpl Executor) {
	if m.templates == nil {
		m.templates = make(map[string]Executor)
	}

	m.templates[name] = tmpl
}

// ExecuteTemplateByte executes a template by name with the given data.
func (m *Manager) ExecuteTemplateByte(name string, data interface{}) ([]byte, error) {
	tmpl, err := m.GetTemplate(name)
	if err != nil {
		return nil, err
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil {
		return nil, fmt.Errorf("error executing template %s: %w", name, err)
	}

	return buf.Bytes(), nil
}

// ExecuteTemplate ...
func (m *Manager) ExecuteTemplate(name string, data interface{}) (string, error) {
	b, err := m.ExecuteTemplateByte(name, data)
	if err != nil {
		return "", err
	}
	return string(b), nil
}
