// Package server ...
package server

import (
	"context"
	"crypto/ecdsa"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"fmt"
	"os"
	"path/filepath"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/dbmy/foobar/constants"
	"gitlab.myteksi.net/dbmy/foobar/handlers"
	"gitlab.myteksi.net/dbmy/foobar/kafka/consumers"
	"gitlab.myteksi.net/dbmy/foobar/server/config"
	"gitlab.myteksi.net/dbmy/foobar/storage"
	"gitlab.myteksi.net/dbmy/foobar/template"
	"gitlab.myteksi.net/dbmy/foobar/utils/cache"
)

// Serve ...
func Serve() {
	appCfg := &config.AppConfig{}
	app := servus.Default(
		servus.WithAppConfig(appCfg))
	todoDao := storage.NewTodoDAO(appCfg.DataConfig(), nil)
	logger := app.GetLogger()
	key, _ := ParseECDSAPrivateKey(logger, []byte(appCfg.CardInfoStaticPrivateKey))
	slackBotURL := appCfg.SlackBotURL
	redisClient := cache.Init(app, appCfg)

	service := &handlers.FoobarService{
		TodoDAO:     todoDao,
		PrivateKey:  key,
		SmsURL:      slackBotURL,
		RedisClient: redisClient,
		Config:      appCfg,
	}
	service.RegisterRoutes(app)

	// initialize workflows
	if os.Getenv(constants.FeatureGateWorkflow) == "true" {
		workflowengine.Init(
			appCfg.Data,
			workflowengine.WithCache(redisClient),
			workflowengine.WithMonitoring(app.GetStatsD()),
			workflowengine.WithTracing(app.GetTracer()),
		)
	}
	registerPublisher(app, appCfg)
	ctx := slog.NewContextWithLogger(context.Background(), logger)
	if os.Getenv(constants.FeatureGateWorkflow) == "true" {
		initializeWorkflow(app)
		workflowengine.StartWorkers()
	}
	consumers.Init(ctx, appCfg)

	// Initialize template singleton and load templates
	templateManager := template.GetInstance()
	templateDir := filepath.Join("resources", "html")
	templateManager.LoadTemplates(slog.NewContextWithLogger(ctx, app.GetLogger()), templateDir)

	app.OnShutdown(func(ctx context.Context) {
		if os.Getenv(constants.FeatureGateWorkflow) == "true" {
			workflowengine.StopWorkers(ctx)
		}
		consumers.Stop()
	})

	app.Run()
}

var ParseECDSAPrivateKey = func(logger slog.YallLogger, input []byte) (*ecdsa.PrivateKey, error) {
	block, _ := pem.Decode(input)
	if block == nil {
		logger.Error("ParseECDSAPrivateKey", "failed to decode ecdsa private key")
		return nil, errors.New("failed to decode ecdsa private key")
	}
	priv, err := x509.ParseECPrivateKey(block.Bytes)
	if err != nil {
		logger.Error("ParseECDSAPrivateKey", fmt.Sprintf("failed to parse private key pem block, err:%v", err))
		return nil, err
	}

	return priv, nil
}
