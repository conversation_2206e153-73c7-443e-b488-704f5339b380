// Package config ...
package config

import (
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"

	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2"
)

// Loader ...
type Loader struct{}

// AppConfig ...
type AppConfig struct {
	servus.DefaultAppConfig
	CardInfoStaticPrivateKey      string                        `json:"cardInfoStaticPrivateKey"`
	SlackBotURL                   string                        `json:"slackBotURL"`
	RedisConfig                   *redis.Config                 `json:"redisConfig"`
	LoanCoreLOCAccountKafkaConfig LoanCoreLOCAccountKafkaConfig `json:"loanCoreLOCAccountKafkaConfig"`
	TestCanaryIsError             bool                          `json:"testCanaryIsError"`
}

// Name ...
func (a *AppConfig) Name() string {
	return a.ServiceName
}

// GetOwnerInfo ...
func (a *AppConfig) GetOwnerInfo() servus.OwnerInfo {
	return a.OwnerInfo
}

// LogConfig ...
func (a *AppConfig) LogConfig() *servus.LogConfig {
	return a.Logger
}

// StatsDConfig ...
func (a *AppConfig) StatsDConfig() *servus.StatsDConfig {
	return a.StatsD
}

// GetHost ...
func (a *AppConfig) GetHost() string {
	return a.Host
}

// GetPort ...
func (a *AppConfig) GetPort() int {
	return a.Port
}

// GetDescription ...
func (a *AppConfig) GetDescription() string {
	return a.Description
}

// Load loads your application's configuration
func (l *Loader) Load(appCfg interface{}) error {
	// implement config loading
	return nil
}

// LoanCoreLOCAccountKafkaConfig ...
type LoanCoreLOCAccountKafkaConfig struct {
	*sndconfig.KafkaConfig
}

// GrabDefenceConfig ...
type GrabDefenceConfig struct {
	HostAddress       string `json:"hostAddress"`
	TenantID          string `json:"tenantID"`
	AppID             string `json:"appID"`
	Aud               string `json:"aud"`
	RsaPrivateKeyPem  string `json:"rsaPrivateKeyPem"`
	WhitelistEndpoint string `json:"whitelistEndpoint"`
}

type SlackConfig struct {
	BotToken string `json:"botToken"`
	AppToken string `json:"appToken"`
}
