package server

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/schemas/streams"
	streamsapi "gitlab.myteksi.net/dakota/schemas/streams/apis/loan_core_loc"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dbmy/foobar/kafka/publishers"
	"gitlab.myteksi.net/dbmy/foobar/server/config"
	"gitlab.myteksi.net/dbmy/foobar/workflow/retry_attempt"
)

func initializeWorkflow(app *servus.Application) {
	// locCreationWorkflow := &create_account.WorkflowImpl{}
	// app.MustRegister("workflow.createLOC", locCreationWorkflow)
	// locCreationWorkflow.Register()
	retryAttemptWorkflow := &retry_attempt.WorkflowImpl{}
	app.MustRegister("workflow.retryAttempt", retryAttemptWorkflow)
	retryAttemptWorkflow.Register()
}

func registerPublisher(app *servus.Application, conf *config.AppConfig) {
	// if os.Getenv(constants.FeatureGateWorkflow) == "true" {
	// 	registerLOCPublisher(app, conf)
	// }
}

func registerLOCPublisher(app *servus.Application, conf *config.AppConfig) {
	kafkaWriter, err := streams.NewStaticWriter(
		context.Background(), "loanCore",
		*conf.LoanCoreLOCAccountKafkaConfig.KafkaConfig,
		&streamsapi.LoanCoreLoc{},
	)
	if err != nil {
		panic(fmt.Sprintf("failed to create loan core kafka writer: %v", err))
	}
	app.MustRegister("writer.locStream", kafkaWriter)
	app.MustRegister("publisher.loc", &publishers.PublisherImpl{})
}
