// Package cache provide utility methods to use caching functionality
package cache

import (
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dbmy/foobar/server/config"
)

var (
	// RedisClient defines the redis client
	RedisClient redis.Client
)

// Init initialize the RedisClient
func Init(app *servus.Application, conf *config.AppConfig) redis.Client {
	redisClient := redis.MakeClusterClient(conf.RedisConfig, redis.StatsD(app.GetStatsD()), redis.Logger(app.GetLogger()))
	RedisClient = redisClient
	return redisClient
}
