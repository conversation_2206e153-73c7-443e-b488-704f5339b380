version: '3'
services:
  redis-cluster:
    image: 'grokzen/redis-cluster:latest'
    ports:
      - '7000:7000'
      - '7001:7001'
      - '7002:7002'
      - '7003:7003'
      - '7004:7004'
      - '7005:7005'
    environment:
      INITIAL_PORT: 7000
      IP: 0.0.0.0
  zookeeper:
    image: docker.io/bitnami/zookeeper:3.8
    ports:
      - "2181:2181"
    volumes:
      - "/Users/<USER>/grab-projects/dbmy/kafka-demo/tmp/kafka/docker-local-new:/bitnami" # update the path
    environment:
      - ALLOW_ANONYMOUS_LOGIN=yes
    networks:
      - kafka-demo
  kafka:
    image: docker.io/bitnami/kafka:3.2
    ports:
      - "9092:9092"
      - "9093:9093"
    volumes:
      - "/Users/<USER>/grab-projects/dbmy/kafka-demo/tmp/kafka/docker-local-new:/bitnami" # update the path
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,PLAINTEXT_EXTERNAL:PLAINTEXT
      - KAFKA_CFG_LISTENERS=PLAINTEXT://kafka:9092,PLAINTEXT_EXTERNAL://0.0.0.0:9093
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,PLAINTEXT_EXTERNAL://localhost:9093
      - KAFKA_CFG_ZOOKEEPER_CONNECT=zookeeper:2181
      - ALLOW_PLAINTEXT_LISTENER=yes
      - KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE=true
    depends_on:
      - zookeeper
    networks:
      - kafka-demo
networks:
  kafka-demo: