<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Bonus Pocket Calculator</title>
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/decimal.js/9.0.0/decimal.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body class="container mt-4">
<div id="app">
  <h1>Test Bonus Pocket Calculator</h1>
  
  <form @submit.prevent="calculateBonusPocketInterest" class="mb-3">
    <div class="mb-3 row">
      <div class="col">
        <label class="form-label">Principal Amount:</label>
        <input v-model="bonusPocket.principal" type="text" class="form-control" required />
      </div>
      <div class="col">
        <label class="form-label">Tenor (Days):</label>
        <input v-model="bonusPocket.tenor" type="number" class="form-control" min="1" required />
      </div>
    </div>

    <div class="mb-3 row">
      <div class="col">
        <label class="form-label">Base Interest Rate (%):</label>
        <input v-model="bonusPocket.baseRate" type="text" class="form-control" required />
      </div>
      <div class="col">
        <label class="form-label">Bonus Interest Rate (%):</label>
        <input v-model="bonusPocket.bonusRate" type="text" class="form-control" required />
      </div>
    </div>

    <button type="submit" class="btn btn-primary">Calculate</button>
  </form>

  <!-- Results -->
  <div v-if="bonusPocketResult.projectionTable !== null">
    <h5>Results</h5>
    <div class="alert alert-info">
      <p><strong>Total Base Interest:</strong> {{ bonusPocketResult.totalBaseInterest }}</p>
      <p><strong>Total Bonus Interest:</strong> {{ bonusPocketResult.totalBonusInterest }}</p>
      <p><strong>Final Amount:</strong> {{ bonusPocketResult.finalAmount }}</p>
    </div>
  </div>
</div>

<script>
  Decimal.set({ precision: 28 });
  new Vue({
    el: "#app",
    data() {
      return {
        bonusPocket: {
          principal: '10000',
          tenor: 30,
          baseRate: '3',
          bonusRate: '2',
          daysInYear: 365
        },
        bonusPocketResult: {
          projectionTable: null,
          totalBaseInterest: null,
          totalBonusInterest: null,
          finalAmount: null
        }
      };
    },
    methods: {
      calculateBonusPocketInterest() {
        const principal = new Decimal(this.bonusPocket.principal);
        const tenor = parseInt(this.bonusPocket.tenor);
        const baseRate = new Decimal(this.bonusPocket.baseRate);
        const bonusRate = new Decimal(this.bonusPocket.bonusRate);
        const daysInYear = new Decimal(this.bonusPocket.daysInYear);

        const baseDailyRate = baseRate.dividedBy(daysInYear).dividedBy(100);
        const bonusDailyRate = bonusRate.dividedBy(daysInYear).dividedBy(100);

        this.generateBonusPocketProjection(principal, tenor, baseDailyRate, bonusDailyRate);
      },

      generateBonusPocketProjection(initialPrincipal, tenor, baseDailyRate, bonusDailyRate) {
        const projectionTable = [];
        let currentPrincipal = initialPrincipal;
        let totalBaseInterest = new Decimal(0);
        let totalBonusInterest = new Decimal(0);

        for (let day = 1; day <= tenor; day++) {
          const dailyBaseInterest = currentPrincipal.times(baseDailyRate);
          const dailyBonusInterest = currentPrincipal.times(bonusDailyRate);

          const dailyBaseInterestProcessed = dailyBaseInterest.toDP(5, Decimal.ROUND_DOWN).toDP(2, Decimal.ROUND_HALF_UP);
          const dailyBonusInterestProcessed = dailyBonusInterest.toDP(5, Decimal.ROUND_DOWN).toDP(2, Decimal.ROUND_HALF_UP);

          projectionTable.push({
            day: day,
            principal: currentPrincipal.toDP(2, Decimal.ROUND_HALF_UP).toString(),
            baseInterest: dailyBaseInterestProcessed.toString(),
            bonusInterest: dailyBonusInterestProcessed.toString()
          });

          totalBaseInterest = totalBaseInterest.plus(dailyBaseInterestProcessed);
          totalBonusInterest = totalBonusInterest.plus(dailyBonusInterestProcessed);

          currentPrincipal = currentPrincipal.plus(dailyBaseInterestProcessed);
        }

        const finalAmount = initialPrincipal.plus(totalBaseInterest).plus(totalBonusInterest);

        this.bonusPocketResult.projectionTable = projectionTable;
        this.bonusPocketResult.totalBaseInterest = totalBaseInterest.toDP(2, Decimal.ROUND_HALF_UP).toString();
        this.bonusPocketResult.totalBonusInterest = totalBonusInterest.toDP(2, Decimal.ROUND_HALF_UP).toString();
        this.bonusPocketResult.finalAmount = finalAmount.toDP(2, Decimal.ROUND_HALF_UP).toString();
      }
    }
  });
</script>
</body>
</html>
