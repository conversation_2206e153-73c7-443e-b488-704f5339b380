package handlers

import (
	context "context"
	"errors"
	"fmt"
	"os"

	"github.com/google/uuid"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
	api "gitlab.myteksi.net/dbmy/foobar/api"
	"gitlab.myteksi.net/dbmy/foobar/constants"
	"gitlab.myteksi.net/dbmy/foobar/dto"
	"gitlab.myteksi.net/dbmy/foobar/workflow/create_account"
)

// WorkflowLOC ...
func (f *FoobarService) WorkflowLOC(ctx context.Context, req *api.Request) (*api.Response, error) {
	if os.Getenv(constants.FeatureGateWorkflow) != "true" {
		slog.FromContext(ctx).Error("servus", "Workflow feature is not enabled")
		return nil, errors.New("workflow feature not enabled")
	}

	data := &create_account.ExecutionData{
		CreateAccountStreamMsg: &dto.CreateAccountStreamMessage{
			Name: "foobar_customer",
		},
	}
	requestID := uuid.New().String()

	err := workflowengine.InitExecution(ctx, workflowengine.Execution{
		WorkflowID: create_account.WorkflowID,
		RequestID:  requestID,
	}, data)
	if err != nil {
		slog.FromContext(ctx).Error(create_account.WorkflowID, fmt.Sprintf("failed to init workflow: %s", err.Error()))
		return nil, err
	}
	execData, err := workflowengine.Execute(ctx, workflowengine.Execution{
		WorkflowID:     create_account.WorkflowID,
		RequestID:      requestID,
		ExecutionEvent: create_account.EvPersistDB,
	}, data)

	if err != nil {
		slog.FromContext(ctx).Error(create_account.WorkflowID, fmt.Sprintf("failed to execute workflow: %s", err.Error()))
		return nil, err
	}
	resp, err := execData.Marshal()
	if err != nil {
		return nil, err
	}
	return &api.Response{Value: string(resp)}, nil
}
