package handlers

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dbmy/foobar/api"
)

func TestPartnerTxnValidation(t *testing.T) {
	service := &FoobarService{}

	t.Run("happy-path", func(t *testing.T) {
		resp, _ := service.PartnerTxnValidation(context.Background(), &api.PartnerTxnRequest{
			Partner: "a-partner",
			Payload: &api.Payload{
				CustomerID: "",
				IpAddress:  "",
				DeviceID:   "",
				Category:   "",
				CreatedAt:  time.Now(),
			},
		})
		assert.Equal(t, &api.PartnerTxnResponse{
			Status:       1,
			Action:       0,
			Reason:       "",
			ExtraMessage: "",
		}, resp)
	})

	t.Run("empty-payload", func(t *testing.T) {
		resp, _ := service.PartnerTxnValidation(context.Background(), &api.PartnerTxnRequest{
			Partner: "a-partner",
			Payload: &api.Payload{},
		})
		assert.Equal(t, &api.PartnerTxnResponse{
			Status:       1,
			Action:       0,
			Reason:       "",
			ExtraMessage: "",
		}, resp)
	})

}
