// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package handlers

import (
	context "context"
	"net/http"
	"runtime/debug"

	v2 "gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	api "gitlab.myteksi.net/dbmy/foobar/api"
	"gitlab.myteksi.net/dbmy/foobar/template"
)

type handlerFunc func(ctx context.Context, req interface{}) (interface{}, error)

func withPanicRecovery(h handlerFunc) handlerFunc {
	return func(ctx context.Context, req interface{}) (res interface{}, err error) {
		defer func() {
			if anyPanic := recover(); anyPanic != nil {
				slog.FromContext(ctx).Error("servus", "[PANIC RECOVERED]", slog.CustomTag("err", anyPanic),
					slog.CustomTag("stack_trace", string(debug.Stack())))
				err = v2.ServiceError{
					HTTPCode: 500,
					Code:     "0",
					Message:  "internal server error",
				}
			}
		}()
		res, err = h(ctx, req)
		return res, err
	}
}

// RegisterRoutes registers handlers with the Servus library.
func (f *FoobarService) RegisterRoutes(app *v2.Application) {
	app.POST(
		"/api/v1/example",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := f.Example(ctx, req.(*api.Request))
			return res, err
		},
		v2.WithRequest(&api.Request{}),
		v2.WithResponse(&api.Response{}),
	)
	app.POST(
		"/v1/sms",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := f.Sms(ctx, req.(*api.SmsRequest))
			return res, err
		},
		v2.WithRequest(&api.SmsRequest{}),
		v2.WithResponse(&api.SmsResponse{}),
		v2.WithDescription("Sms publishes a SMS to specified phone number using pre-defined template."),
	)
	app.GET(
		"/v1/auth-check",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := f.AuthCheck(ctx, req.(*api.Request))
			return res, err
		},
		v2.WithRequest(&api.Request{}),
		v2.WithResponse(&api.Response{}),
		v2.WithDescription("Authentication Check."),
	)
	app.POST(
		"/v1/risk-check",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := f.RiskCheck(ctx, req.(*api.RiskCheckRequest))
			return res, err
		},
		v2.WithRequest(&api.RiskCheckRequest{}),
		v2.WithResponse(&api.RiskCheckResponse{}),
	)
	app.POST(
		"/risk/api/v2/validate/partnertxn",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := f.PartnerTxnValidation(ctx, req.(*api.PartnerTxnRequest))
			return res, err
		},
		v2.WithRequest(&api.PartnerTxnRequest{}),
		v2.WithResponse(&api.PartnerTxnResponse{}),
	)
	app.GET(
		"/test/get",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := f.TestGet(ctx, req.(*api.TestGetRequest))
			return res, err
		},
		v2.WithRequest(&api.TestGetRequest{}),
		v2.WithResponse(&api.TestGetResponse{}),
	)
	app.POST(
		"/v1/email",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := f.Email(ctx, req.(*api.EmailRequest))
			return res, err
		},
		v2.WithRequest(&api.EmailRequest{}),
		v2.WithResponse(&api.EmailResponse{}),
		v2.WithDescription("Email sends a email to specified address using pre-defined template."),
	)
	app.POST(
		"/hedwig/v1/push",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := f.Push(ctx, req.(*api.PushRequest))
			return res, err
		},
		v2.WithRequest(&api.PushRequest{}),
		v2.WithResponse(&api.PushResponse{}),
		v2.WithDescription("Push sends a push notification to specified phone using pre-defined template."),
	)
	app.POST(
		"/workflow/v1/create_loc",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := f.CreateLoc(ctx, req.(*api.Request))
			return res, err
		},
		v2.WithRequest(&api.Request{}),
		v2.WithResponse(&api.Response{}),
		v2.WithDescription("Push sends a push notification to specified phone using pre-defined template."),
	)
	app.GET(
		"/v1/comms/otp",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := f.RetrieveOTP(ctx, req.(*api.RetrieveOTPRequest))
			return res, err
		},
		v2.WithRequest(&api.RetrieveOTPRequest{}),
		v2.WithResponse(&api.RetrieveOTPResponse{}),
	)
	app.GET(
		"/v1/canary",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			err := f.TestCanary(ctx)
			return nil, err
		},
		v2.WithDescription("Returns response code depending on config. Use to test canary rollout"),
	)
	app.GET(
		"/lending/pdf-tamper-checker",
		withPanicRecovery(func(ctx context.Context, req interface{}) (interface{}, error) {
			// Get the template manager instance
			templateManager := template.GetInstance()

			// Execute the template with empty data (no dynamic content needed)
			html, err := templateManager.ExecuteTemplateByte("statement-detect.tpl.html", nil)
			if err != nil {
				return nil, err
			}

			// Return the HTML content as a Response
			return html, nil
		}),
		v2.WithRequest(&api.Request{}),
		v2.WithResponse(&api.Response{}),
		v2.WithEncoder(func(respWriter http.ResponseWriter, resp interface{}) error {
			respByte, ok := resp.([]byte)
			if !ok {
				return v2.InternalServerError("0", "failed to convert response to byte slice")
			}
			respWriter.Header().Set("Access-Control-Allow-Origin", "*")
			respWriter.Header().Set("Content-Type", "text/html")
			respWriter.WriteHeader(http.StatusOK)
			respWriter.Write(respByte)
			return nil
		}),
		v2.WithDescription("Returns a pdf statement checker for tamper checking"),
	)
	app.GET(
		"/grabfood/partner/v1/bank/onboarding-mex-metadata",
		func(ctx context.Context, req interface{}) (interface{}, error) {
			res, err := f.GrabMex(ctx, req.(*api.GrabMexRequest))
			return res, err
		},
		v2.WithRequest(&api.GrabMexRequest{}),
		v2.WithResponse(&api.GrabMexResponse{}),
	)
	app.GET(
		"/deposits/casa-interest-simulation",
		withPanicRecovery(func(ctx context.Context, req interface{}) (interface{}, error) {
			// Get the template manager instance
			templateManager := template.GetInstance()

			// Execute the template with empty data (no dynamic content needed)
			html, err := templateManager.ExecuteTemplateByte("casa-interest-calculation.tpl.html", nil)
			if err != nil {
				return nil, err
			}

			// Return the HTML content as a Response
			return html, nil
		}),
		v2.WithRequest(&api.Request{}),
		v2.WithResponse(&api.Response{}),
		v2.WithEncoder(func(respWriter http.ResponseWriter, resp interface{}) error {
			respByte, ok := resp.([]byte)
			if !ok {
				return v2.InternalServerError("0", "failed to convert response to byte slice")
			}
			respWriter.Header().Set("Access-Control-Allow-Origin", "*")
			respWriter.Header().Set("Content-Type", "text/html")
			respWriter.WriteHeader(http.StatusOK)
			respWriter.Write(respByte)
			return nil
		}),
		v2.WithDescription("Returns a CASA saving interest calculator"),
	)
}
