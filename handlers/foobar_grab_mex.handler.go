package handlers

import (
	context "context"
	api "gitlab.myteksi.net/dbmy/foobar/api"
)

func (f *FoobarService) GrabMex(ctx context.Context, req *api.GrabMexRequest) (*api.GrabMexResponse, error) {
	// TODO: complete this handler with business logic.

	return &api.GrabMexResponse{
		Last_1MonthTransCount:   0,
		Last_3MonthsTransCount:  0,
		Last_6MonthsTransCount:  0,
		Last_12MonthsTransCount: 0,
		GrabActiveFlag:          "ACTIVE",
		GrabBlackListFlag:       "",
		Stores:                  nil,
		HasMore:                 false,
	}, nil
}
