package handlers

import (
	"bytes"
	context "context"
	"encoding/json"
	"net/http"

	api "gitlab.myteksi.net/dbmy/foobar/api"
)

// Email sends a email to specified address using pre-defined template.
func (f *FoobarService) Email(ctx context.Context, req *api.EmailRequest) (*api.EmailResponse, error) {
	// return dummy response to unblock error from grab-id
	var response *api.EmailResponse
	response = &api.EmailResponse{
		MessageID: "12345",
	}
	// Get email and code(OTP)
	email := req.RecipientID
	code := req.Template.Params["code"]
	// Format message
	values := map[string]string{
		"text": "Email: " + email + "\n" + "Code: " + code,
	}

	// Convert to json and send OTP to slack
	jsonValue, _ := json.Marshal(values)
	_, err := http.Post(f.SmsURL, "application/json", bytes.NewBuffer(jsonValue))
	if err != nil {
		return nil, err
	}
	return response, nil
}
