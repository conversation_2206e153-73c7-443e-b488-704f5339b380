package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dbmy/foobar/api"
)

func TestEmail(t *testing.T) {
	verMock := mockFoobarService("*********************************************************************************")

	t.Run("happy-path", func(t *testing.T) {
		resp, _ := verMock.Email(context.Background(), &api.EmailRequest{
			RecipientID: "<EMAIL>",
			Template: &api.Template{
				ID: "12345",
				Params: map[string]string{
					"code": "601899",
				},
			},
		})
		assert.Equal(t, &api.EmailResponse{
			MessageID: "12345",
		}, resp)
	})

	verMock2 := mockFoobarService("")

	t.Run("broken-url", func(t *testing.T) {
		_, err := verMock2.Email(context.Background(), &api.EmailRequest{
			RecipientID: "<EMAIL>",
			Template: &api.Template{
				ID: "12345",
				Params: map[string]string{
					"code": "601899",
				},
			},
		})
		e := err.Error() == "invalid slack URL"
		assert.Equal(t, false, e)
		assert.EqualError(t, err, "Post \"\": unsupported protocol scheme \"\"")

	})

}
