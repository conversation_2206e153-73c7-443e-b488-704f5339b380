package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dbmy/foobar/api"
)

func TestSms(t *testing.T) {
	verMock := mockFoobarService("*********************************************************************************")

	t.Run("happy-path", func(t *testing.T) {
		resp, _ := verMock.Sms(context.Background(), &api.SmsRequest{
			RecipientID: "+60123345679",
			Template: &api.Template{
				ID: "12345",
				Params: map[string]string{
					"otp": "601899",
				},
			},
		})
		assert.Equal(t, &api.SmsResponse{
			MessageID:       "12345",
			VendorMessageID: "12345",
			VendorInfo:      "foobar",
		}, resp)
	})

	verMock2 := mockFoobarService("")

	t.Run("broken-url", func(t *testing.T) {
		_, err := verMock2.Sms(context.Background(), &api.SmsRequest{
			RecipientID: "+60123345679",
			Template: &api.Template{
				ID: "12345",
				Params: map[string]string{
					"Otp": "601899",
				},
			},
		})
		e := err.Error() == "invalid slack URL"
		assert.Equal(t, false, e)
		assert.EqualError(t, err, "Post \"\": unsupported protocol scheme \"\"")

	})

}

func mockFoobarService(url string) FoobarService {
	return FoobarService{
		SmsURL: url,
	}
}
