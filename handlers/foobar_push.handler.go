package handlers

import (
	"bytes"
	context "context"
	"encoding/json"
	"net/http"

	api "gitlab.myteksi.net/dbmy/foobar/api"
)

// Push sends a push notification to specified phone using pre-defined template.
func (f *FoobarService) Push(ctx context.Context, req *api.PushRequest) (*api.PushResponse, error) {
	resp := &api.PushResponse{
		MessageID: "12345",
		Message:   "successful",
	}

	// Get safe id and successful notification message
	safeID := req.RecipientID
	message := req.Template.ID
	// Format message
	values := map[string]string{
		"text": "User ID: " + safeID + "\n" + "Message: " + message,
	}

	// Convert to json and send push notification to slack
	jsonValue, _ := json.Marshal(values)
	_, err := http.Post(f.SmsURL, "application/json", bytes.NewBuffer(jsonValue))
	if err != nil {
		return nil, err
	}

	return resp, nil
}
