package handlers

import (
	"context"
	"encoding/json"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dbmy/foobar/api"
	"net/http"
)

func (f *FoobarService) RetrieveOTP(ctx context.Context, req *api.RetrieveOTPRequest) (*api.RetrieveOTPResponse, error) {
	slog.FromContext(ctx).Info(logTag, "received OTP retrieve request")
	jsonBytes, err := f.RedisClient.GetBytes(ctx, generateRedisKey(req.Identifier))
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to get OTP from redis", slog.Error(err))
		if err == redis.ErrNoData {
			return nil, servus.ServiceError{
				HTTPCode: http.StatusConflict,
				Code:     "NOT_FOUND",
				Message:  "entry not found",
			}
		}
		return nil, err
	}

	cacheObj := &otpCache{}
	_ = json.Unmarshal(jsonBytes, cacheObj)

	slog.FromContext(ctx).Info(logTag, "success to get OTP from redis")
	return &api.RetrieveOTPResponse{
		Status: success,
		Proof:  cacheObj.Proof,
	}, nil
}
