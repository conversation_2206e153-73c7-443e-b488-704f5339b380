package handlers

import (
	context "context"
	"fmt"
	"strconv"

	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.myteksi.net/dakota/servus/v2/data"

	api "gitlab.myteksi.net/dbmy/foobar/api"
)

// Example ...
func (f *FoobarService) Example(ctx context.Context, req *api.Request) (*api.Response, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("foo", "foo"))
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("bar", "bar"))
	slog.FromContext(ctx).Info("Example", "testing tag")
	testContextLogging(ctx)
	id, _ := strconv.Atoi(req.Value)
	query := []data.Condition{data.EqualTo("ID", id)}
	todos, err := f.TodoDAO.Find(ctx, query...)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	return &api.Response{Value: todos[0].Author}, nil
}

func testContextLogging(ctx context.Context) {
	slog.FromContext(ctx).Info("Example", "logging with context tags from another function", slog.CustomTag("foobar", "foobar"))
}
