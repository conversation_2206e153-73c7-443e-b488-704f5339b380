package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.myteksi.net/dbmy/foobar/api"
)

func TestPush(t *testing.T) {
	verMock := mockFoobarService("*********************************************************************************")

	t.Run("happy-path", func(t *testing.T) {
		resp, _ := verMock.Push(context.Background(), &api.PushRequest{
			RecipientID: "abc123",
			Template: &api.Template{
				ID: "updated_phone_success_push_notification",
			},
		})
		assert.Equal(t, &api.PushResponse{
			MessageID: "12345",
			Message:   "successful",
		}, resp)
	})

	verMock2 := mockFoobarService("")

	t.Run("broken-url", func(t *testing.T) {
		_, err := verMock2.Push(context.Background(), &api.PushRequest{
			RecipientID: "abc123",
			Template: &api.Template{
				ID: "updated_phone_success_push_notification",
			},
		})
		e := err.Error() == "invalid slack URL"
		assert.Equal(t, false, e)
		assert.EqualError(t, err, "Post \"\": unsupported protocol scheme \"\"")

	})

}
