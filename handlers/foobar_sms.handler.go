package handlers

import (
	context "context"
	"encoding/json"
	"fmt"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	api "gitlab.myteksi.net/dbmy/foobar/api"
	"time"
)

const (
	logTag      = "otp_client"
	redisOTPKey = "otp_code_proof_%s"
	success     = "success"
	failed      = "failed"
)

// Sms publishes SMS to specified phone number using pre-defined template and store OTP to redis.
func (f *FoobarService) Sms(ctx context.Context, req *api.SmsRequest) (*api.SmsResponse, error) {
	// return dummy response to unblock error from grab-id
	var response *api.SmsResponse
	response = &api.SmsResponse{
		MessageID:       "12345",
		VendorInfo:      "foobar",
		VendorMessageID: "12345",
	}
	// Get phone number and OTP
	phoneNumber := req.RecipientID
	otp := req.Template.Params["otp"]
	// Format message
	//values := map[string]string{
	//	"text": "Phone number: " + phoneNumber + "\n" + "Otp: " + otp,
	//}

	// Convert to json and send OTP to slack
	//jsonValue, _ := json.Marshal(values)
	//_, err := http.Post(f.SmsURL, "application/json", bytes.NewBuffer(jsonValue))
	//if err != nil {
	//	return nil, err
	//}

	_ = f.storeOtpCacheToRedis(ctx, phoneNumber, otp)

	return response, nil
}

// store OTP to redis ...
func (f *FoobarService) storeOtpCacheToRedis(ctx context.Context, phoneNumber string, otp string) error {
	// Insert OTP
	cacheObj := &otpCache{
		RefID:      "12345",
		Identifier: phoneNumber,
		Proof:      otp,
		Channel:    "phone",
	}
	jsonBytes, _ := json.Marshal(cacheObj)
	isSuccess, err := f.RedisClient.Set(ctx, generateRedisKey(phoneNumber), jsonBytes, 15*time.Minute)

	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to set OTP to redis", slog.Error(err))
		if err != redis.ErrAlreadyExists {
			return err
		}
	}

	if !isSuccess {
		slog.FromContext(ctx).Warn(logTag, "failed to set OTP to redis", slog.Error(err))
		return servus.InternalServerError("INTERNAL_ERROR", "Failed to set OTP to redis")
	}

	return nil
}

func generateRedisKey(key string) string {
	return fmt.Sprintf(redisOTPKey, key)
}

type otpCache struct {
	RefID      string `json:"refID"`
	Identifier string `json:"identifier"`
	Proof      string `json:"proof"`
	Channel    string `json:"channel"`
}
