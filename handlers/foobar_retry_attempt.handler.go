package handlers

import (
	context "context"
	"fmt"
	"net/http"

	"github.com/google/uuid"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/dbmy/foobar/api"
	"gitlab.myteksi.net/dbmy/foobar/workflow/retry_attempt"
)

// WorkflowRetryAttempt ...
func (f *FoobarService) WorkflowRetryAttempt(ctx context.Context, req *api.Request) (*api.Response, error) {
	idempotencyKey := uuid.New().String()
	data, err := retry_attempt.InitExecution(ctx, idempotencyKey)
	if err != nil {
		slog.FromContext(ctx).Error(retry_attempt.WorkflowID, fmt.Sprintf("failed to init workflow idempotency_key=%s: %s", idempotencyKey, err.Error()))
		return nil, &servus.ServiceError{
			HTTPCode: http.StatusInternalServerError,
			Code:     "500",
			Message:  err.Error(),
			Errors:   nil,
		}
	}
	state := data.GetState()
	slog.FromContext(ctx).Info(retry_attempt.WorkflowID, fmt.Sprintf("initiated workflow execution with idempotency_key=%s state=%d", idempotencyKey, state))

	_, err = we.Execute(ctx, we.Execution{
		WorkflowID:     retry_attempt.WorkflowID,
		RequestID:      idempotencyKey,
		ExecutionEvent: retry_attempt.EvNoNeed,
	}, nil)

	if err != nil {
		slog.FromContext(ctx).Error(retry_attempt.WorkflowID, fmt.Sprintf("failed to execute workflow idempotency_key=%s state=%d: %s", idempotencyKey, state, err.Error()))
		return nil, &servus.ServiceError{
			HTTPCode: http.StatusInternalServerError,
			Code:     "500",
			Message:  err.Error(),
			Errors:   nil,
		}
	}
	return &api.Response{Value: idempotencyKey}, nil
}
