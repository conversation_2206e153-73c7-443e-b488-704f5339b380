package handlers

import (
	context "context"

	api "gitlab.myteksi.net/dbmy/foobar/api"
)

func (f *FoobarService) RiskCheck(ctx context.Context, req *api.RiskCheckRequest) (*api.RiskCheckResponse, error) {
	resp := &api.RiskCheckResponse{
		AppStatusCode: 1,
		AppStatusDesc: "test",
		Data: &api.RiskCheckResponseData{
			TransactionID: "B46271CA-E16B-4D74-BA83-28D73C3FFE92",
			MfaRequired:   true,
			MfaMech:       "SELFIE&PIN_PLUS",
			UserSafeID:    "19D838B3-D994-40E4-99BB-0820BC33F357",
			AuxInfo:       "test",
		},
	}
	return resp, nil
}
