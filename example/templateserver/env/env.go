// Package env ...
// nolint
package env

import (
	"fmt"
	"os"
)

func Init() {
	os.Getwd()
	pwd, _ := os.Getwd()
	fmt.Printf("current wd: %s", pwd)
	// create your own service conf in this path
	os.Setenv("SERVICE_CONF", pwd+"/temp/template_server/service-conf.json")
	os.Setenv("SERVICE_NAME", "foobar")
	os.Setenv("LOCAL", "true")
	// os.Setenv("SECRET_CONF", pwd+"/vault/secrets/")
	os.Setenv("SECRET_CONF", "")
	os.Setenv("ALL_DEPRECATED_INITS_MIGRATED", "true")
	os.Setenv("MYSQL_HOST", "foobar-db.backend.dev.g-bank.app")
	os.Setenv("DB_NAME", "foobar_dev")
}
