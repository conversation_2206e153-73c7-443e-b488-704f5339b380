package main

import (
	"context"
	"fmt"
	"path/filepath"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dbmy/foobar/example/templateserver/env"
	"gitlab.myteksi.net/dbmy/foobar/handlers"
	"gitlab.myteksi.net/dbmy/foobar/server/config"
	"gitlab.myteksi.net/dbmy/foobar/template"
)

func main() {
	env.Init()
	Serve()
}

// Serve ...
//
//nolint:funlen,staticcheck,gocognit
func Serve() {
	appCfg := &config.AppConfig{}

	fmt.Println("construct new app")
	app := servus.New(
		servus.WithConfigStore(servus.NewDefaultConfigStore()),
		servus.WithPanicRecovery(),
		servus.WithAppConfig(appCfg),
		servus.WithTimeout(time.Hour*30),
		servus.WithLogging(),
	)
	ctx := slog.NewContextWithLogger(context.Background(), app.GetLogger())
	fmt.Println("loading templates")
	templateManager := template.GetInstance()
	templateDir := filepath.Join("resources", "html")
	templateManager.LoadTemplates(ctx, templateDir)

	service := &handlers.FoobarService{}
	fmt.Printf("config loaded: %v\n", appCfg)

	if appCfg.Environment != "prd" {
		service.RegisterRoutes(app)
	}

	app.Run()
}
