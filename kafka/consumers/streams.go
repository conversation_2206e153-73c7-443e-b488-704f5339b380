package consumers

import (
	"context"
	"fmt"
	"time"

	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dbmy/foobar/server/config"

	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
)

const (
	streamDefaultStopTimeout                  = 8 * time.Second
	topicTag                                  = "topic"
	logTag                                    = "stream.consumers"
	dtoNameTag                                = "dtoName"
	loanCoreStream           streams.StreamID = "loanCoreStream"
)

var (
	wg     = gconcurrent.NewExecutionGroup()
	logger = slog.FallbackLogger()
)

// Init initializes stream consumers.
func Init(ctx context.Context, conf *config.AppConfig) {
	logger = slog.FromContext(ctx)
	// if os.Getenv(constants.FeatureGateWorkflow) == "true" {
	// 	initLOCConsumer(ctx, conf)
	// }
}

// Stop ...
func Stop() {
	if locConsumer != nil {
		err := locConsumer.Shutdown()
		if err != nil {
			logger.Error(locConsumerTag, fmt.Sprintf("Error while shutdown loan core loc consumer: %s\n", err.Error()))
		}
	}
	timeoutErr := wg.WaitForDone(streamDefaultStopTimeout)
	if timeoutErr != nil {
		logger.Error(logTag, "timeout on stopping streams, it could lead to a data loss")
	}
}
