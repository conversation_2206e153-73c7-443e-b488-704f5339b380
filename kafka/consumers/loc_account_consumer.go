package consumers

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/loan_core_loc"
	"gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/dbmy/foobar/server/config"
	"gitlab.myteksi.net/dbmy/foobar/workflow/create_account"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"
)

const (
	locConsumerTag = "consumer.loanCoreConsumer"
)

var (
	locConsumer kafkareader.Client
)

func initLOCConsumer(ctx context.Context, conf *config.AppConfig) {
	reader, err := streams.NewStaticReader(context.Background(), loanCoreStream,
		*conf.LoanCoreLOCAccountKafkaConfig.KafkaConfig, &loan_core_loc.LoanCoreLoc{})

	if err != nil {
		logger.Error("consumer.init", fmt.Sprintf("unable to init loan core consumer: %s\n", err.Error()))
		panic(err)
	}
	locConsumer = reader
	eventCh := reader.GetDataChan()

	wg.Go(locConsumerTag, func() {
		for event := range eventCh {
			eventData, ok := event.Event.(*loan_core_loc.LoanCoreLoc)
			if !ok {
				logger.Error(locConsumerTag, fmt.Sprintf("invalid loan core event in loan core loc consumer\n"))
				continue
			}
			// Get the transition ID from the message and trigger the workflow
			transitionID := eventData.ReferenceID
			executeData, err := workflowengine.Execute(ctx, workflowengine.Execution{
				WorkflowID:     create_account.WorkflowID,
				TransitionID:   transitionID,
				ExecutionEvent: create_account.EvPersistStream,
			}, interface{}(eventData))
			if err != nil {
				logger.Error(locConsumerTag, fmt.Sprintf("error in execute loc workflow on event %p: %s", eventData, err.Error()))
				continue
			}
			res, err := executeData.Marshal()
			if err != nil {
				logger.Error(locConsumerTag, fmt.Sprintf("workflow execution successfully but failed to marshal result: %s", err.Error()))
			} else {
				logger.Info(locConsumerTag, fmt.Sprintf("workflow execution successfully with result: %s", string(res)))
			}
		}
	})
}
