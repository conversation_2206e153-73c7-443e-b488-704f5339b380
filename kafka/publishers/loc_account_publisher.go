package publishers

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/loan_core_loc"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkawriter"
)

// PublisherImpl implements Publisher
type PublisherImpl struct {
	KafkaWriter kafkawriter.Client `inject:"writer.locStream"`
}

// Publish loc events to kafka
func (p PublisherImpl) Publish(ctx context.Context, i interface{}) error {
	locDTO, _ := i.(*loan_core_loc.LoanCoreLoc)
	if err := p.KafkaWriter.Save(locDTO); err != nil {
		fmt.Printf("error in publish loan core loc event: %v\n", err)
		return err
	}
	fmt.Printf("published loan core loc event: %v\n", *locDTO)
	return nil
}
