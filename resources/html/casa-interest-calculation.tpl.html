<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GX Bank Interest Calculation (V1.4)</title>
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/decimal.js/9.0.0/decimal.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <style>
    /* Bonus Pocket Scrollable Table Styles */
    .bonus-pocket-table-container {
      overflow-y: auto;
      border: 1px solid #dee2e6;
      border-radius: 0.375rem;
    }

    .bonus-pocket-scrollable .bonus-pocket-table-container {
      max-height: 400px; /* Default fallback */
    }

    .bonus-pocket-table-container .sticky-top {
      position: sticky;
      top: 0;
      z-index: 10;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Ensure table borders are consistent in scrollable container */
    .bonus-pocket-table-container table {
      margin-bottom: 0;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
      .bonus-pocket-table-container {
        font-size: 0.875rem;
      }

      .bonus-pocket-scrollable .bonus-pocket-table-container {
        max-height: 300px;
      }
    }
  </style>
</head>
<body class="container mt-4">
<div id="app">
  <h1 class="text-center mb-4">GX Bank Interest Calculation (V1.5)</h1>

  <div class="container">
    <div class="accordion" id="accordionSection">
      <div class="accordion-item">
        <h2 class="accordion-header" id="headingOne">
          <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#readme-content" aria-expanded="true" aria-controls="collapseOne">
            Readme
          </button>
        </h2>
        <div id="readme-content" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionSection">
          <div class="accordion-body">
            <ul>
              <li>
                This simulator is available for download <a href="https://gxbank.atlassian.net/wiki/spaces/CE/pages/*********/Thought+Machine+Interest+Calculation" target="_blank">here.</a> Its a self contained html page that so long as you have a modern browser and internet connection, you can use it directly without any dependency.
              </li>
              <li>
                The simulator uses <a href="https://speleotrove.com/decimal/decarith.html" target="_blank">decimal arithmetic</a> to perform the computation and precision being used is <strong>28 significant digit</strong> unless otherwise specified. <b><a href="https://en.wikipedia.org/wiki/Significant_figures" target="_blank">Significant digit precision</a> is not same as decimal precision!</b>
              </li>
              <li>
                The simulator is tested based on these <a href="https://docs.google.com/spreadsheets/d/1gFuaUhSc0f3Ya0wyV3KJEyHs98UPgCyBvo0xY2xkYrE/edit#gid=0" target="_blank">test cases</a>. The test results are
                <a href="https://drive.google.com/drive/folders/1LtqnZGjNqXmWGnVR1KNcEBH13EOlPb0X?usp=drive_link" target="_blank">here</a>. Reach out to
                <a href="https://gxbank.slack.com/archives/C07FZ86APCY">Core Banking team</a> should you have query on the test results or simulation results.
              </li>
            </ul>
            <div class="alert alert-danger" role="alert">
              <p>
                This simulator is for reference only. The actual interest calculation is done by the Core Banking system.
              </p>
              <p>
                This simulator is meant for internal use and should not be shared with external parties!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <hr>

  <div class="container">

    <ul class="nav nav-tabs">
      <li class="nav-item">
        <a id="normal-tab-link" class="nav-link" :class="{ active: activeTab === 'normal' }" @click="activeTab = 'normal'" href="#">Normal Interest Breakdown</a>
      </li>
      <li class="nav-item">
        <a id="raya-tab-link" class="nav-link" :class="{ active: activeTab === 'raya' }" @click="activeTab = 'raya'" href="#">Raya Campaign</a>
      </li>
      <li class="nav-item">
        <a id="bonus-pocket-tab-link" class="nav-link" :class="{ active: activeTab === 'bonusPocket' }" @click="activeTab = 'bonusPocket'" href="#">Bonus Pocket</a>
      </li>
    </ul>

    <div v-if="activeTab === 'raya'" class="tab-content mt-3 p-3 rounded bg-light">
      <h4>Raya Campaign Interest Breakdown</h4>
      <form @submit.prevent="calculateRayaInterest" class="mb-3">
        <div class="mb-3 row">
          <div class="col">
            <label class="form-label">Balance:</label>
            <input id="raya-balance-input" v-model="rayaBalance" type="text" class="form-control" required />
          </div>
          <div class="col">
            <label class="form-label">Campaign Threshold:</label>
            <input v-model="campaignThreshold" type="text" class="form-control" disabled
                   pattern="^\d+(\.\d{1,2})?$" title="Should be numeric input up to 2 decimal" required
            />
          </div>
        </div>

        <div class="mb-3">
          <label class="form-label">Normal Interest Rate:</label>
          <input v-model="normalInterestRate" type="text" class="form-control" pattern="^\d+(\.\d{1,2})?$" title="Should be numeric input up to 2 decimal" required />
        </div>


        <div class="mb-3">
          <label class="form-label">Raya Interest Rate:</label>
          <input v-model="rayaInterestRate" type="text" class="form-control" pattern="^\d+(\.\d{1,2})?$" title="Should be numeric input up to 2 decimal" required />
        </div>
        <div class="mb-3">
          <label class="form-label">Days in year</label>
          <input v-model="rayaDaysInYear" type="text" class="form-control" pattern="^\d+" title="Should be integer only" required />
        </div>
        <button id="calculate-raya-btn" type="submit" class="btn btn-primary">Calculate</button>
      </form>
      <div v-if="rayaResult !== null && rayaResult.presence">
        <div id="raya-summary">
          <p class="lead">Customer earned <strong>{{rayaResult.rayaDailyAmountRounded.add(rayaResult.normalDailyAmountT2Rounded)}}</strong> daily total interest (<strong>{{rayaResult.balT2.isZero() ? rayaResult.normalDailyAmountT1Rounded : rayaResult.normalDailyAmountT1Rounded + " + " + rayaResult.normalDailyAmountT2Rounded + " = " + rayaResult.normalDailyAmount}}</strong> daily normal interest, <strong>{{rayaResult.bonusDailyAmount}}</strong> daily bonus interest.</p>
        </div>
        <table class="table" id="raya-table">
          <thead>
          <tr>
            <th>Steps</th>
            <th id="raya-col-header-5perc">5%<p>bal: {{rayaResult.balT1}}</p></th>
            <th id="raya-col-header-3perc-within">3% (within {{formatToK(campaignThreshold)}})<p>bal: {{rayaResult.balT1}}</p></th>
            <th id="raya-col-header-3perc-beyond">3% (beyond {{formatToK(campaignThreshold)}})<p>{{rayaResult.balT2}}</p></th>
            <th>2% (campaign)</th>
          </tr>
          </thead>

          <tbody>
          <tr>
            <td>1. count daily interest rate and truncate into 28 significant figure
              <precision></precision>
            </td>
            <td data-steps="step_1_total">{{rayaResult.rayaDailyRate}}</td>
            <td data-steps="step_1_normal">{{rayaResult.normalDailyRate}}</td>
            <td data-steps="step_1_normal_beyond"></td>
            <td data-steps="step_1_bonus">-</td>
          </tr>
          <tr>
            <td>2. calculate the interest</td>
            <td data-steps="step_2_total">{{rayaResult.rayaDailyRate}} * {{rayaResult.balT1}}(balance within threshold) = {{rayaResult.rayaDailyAmount}}</td>
            <td data-steps="step_2_normal">{{rayaResult.normalDailyRate}} * {{rayaResult.balT1}}(balance within threshold) = {{rayaResult.normalDailyAmountT1}}</td>
            <td data-steps="step_2_normal_beyond">
              {{rayaResult.balT2.isZero() ? '' : rayaResult.normalDailyRate}} * {{rayaResult.balT2}}(balance beyond threshold) = {{rayaResult.normalDailyAmountT2}}
            </td>
            <td data-steps="step_2_bonus">-</td>
          </tr>
          <tr>
            <td>3. truncate the interest into 5 decimal points</td>
            <td data-steps="step_3_total">{{rayaResult.rayaDailyAmountTruncated}}</td>
            <td data-steps="step_3_normal">{{rayaResult.normalDailyAmountTruncated}}</td>
            <td data-steps="step_3_normal_beyond">
              {{rayaResult.balT2.isZero() ? '' : rayaResult.normalDailyAmountT2Truncated}}
            </td>
            <td data-steps="step_3_bonus">-</td>
          </tr>
          <tr>
            <td>4. round half up to 2 decimal points</td>
            <td data-steps="step_4_total">
              <p data-bs-toggle="tooltip" data-bs-placement="top"
                 title="Total interest customer received">
                {{rayaResult.rayaDailyAmountRounded}}
              </p></td>
            <td data-steps="step_4_normal">
              {{rayaResult.normalDailyAmountT1Rounded}}
            </td>
            <td data-steps="step_4_normal_beyond">
              {{rayaResult.balT2.isZero() ? '' : rayaResult.normalDailyAmountT2Rounded}}
            </td>
            <td data-steps="step_4_bonus">-</td>
          </tr>
          <tr>
            <td>5. calculate the interest for campaign interest</td>
            <td data-steps="step_5_total"></td>
            <td data-steps="step_5_normal"></td>
            <td data-steps="step_5_normal_beyond"></td>
            <td data-steps="step_5_bonus">
              {{rayaResult.rayaDailyAmountRounded}}({{rayaRate}} %) - {{rayaResult.normalDailyAmountT1Rounded}}(within {{formatToK(campaignThreshold)}}) = {{rayaResult.bonusDailyAmount}}
            </td>
          </tr>
          <tr>
            <td>Results</td>
            <td data-steps="step_6_total"></td>
            <td data-steps="step_6_normal">{{rayaResult.normalDailyAmountT1Rounded}}</td>
            <td data-steps="step_6_normal_beyond">
              {{rayaResult.balT2.isZero() ? '' : rayaResult.normalDailyAmountT2Rounded}}
            </td>
            <td data-steps="step_6_bonus">{{rayaResult.bonusDailyAmount}}</td>
          </tr>
          </tbody>
        </table>

        <button type="button" class="btn btn-primary" @click="copyTable('raya-table')">Copy Table</button>
      </div>
    </div>

    <div v-if="activeTab === 'bonusPocket'" class="tab-content mt-3 p-3 rounded bg-light">
      <h4>Bonus Pocket Interest Calculation</h4>
      <form @submit.prevent="calculateBonusPocketInterest" class="mb-3">
        <div class="mb-3 row">
          <div class="col">
            <label class="form-label">Principal Amount:</label>
            <input id="bonus-pocket-principal-input" v-model="bonusPocket.principal" type="text" class="form-control"
                   pattern="^\d+(\.\d{1,2})?$" title="Should be numeric input up to 2 decimal" required />
          </div>
          <div class="col">
            <label class="form-label">Calculated Tenor:</label>
            <input type="text" class="form-control bg-light" :value="calculatedTenor + ' days'" readonly
                   title="Automatically calculated from date range" />
          </div>
        </div>

        <div class="mb-3 row">
          <div class="col">
            <label class="form-label">Start Date:</label>
            <input v-model="bonusPocket.startDate" type="date" class="form-control" required
                   @change="validateDateRange" />
          </div>
          <div class="col">
            <label class="form-label">End Date:</label>
            <input v-model="bonusPocket.endDate" type="date" class="form-control" required
                   @change="validateDateRange" :min="bonusPocket.startDate" />
          </div>
        </div>

        <div v-if="dateValidationError" class="alert alert-danger" role="alert">
          {{ dateValidationError }}
        </div>

        <div class="mb-3 row">
          <div class="col">
            <label class="form-label">Base Interest Rate (%):</label>
            <input v-model="bonusPocket.baseRate" type="text" class="form-control"
                   pattern="^\d+(\.\d{1,2})?$" title="Should be numeric input up to 2 decimal" required />
          </div>
          <div class="col">
            <label class="form-label">Bonus Interest Rate (%):</label>
            <input v-model="bonusPocket.bonusRate" type="text" class="form-control"
                   pattern="^\d+(\.\d{1,2})?$" title="Should be numeric input up to 2 decimal" required />
          </div>
        </div>

        <div class="mb-3 row">
          <div class="col">
            <label class="form-label">Days in Year:</label>
            <input v-model="bonusPocket.daysInYear" type="number" class="form-control" required />
          </div>
          <div class="col">
            <label class="form-label">Max Visible Rows:</label>
            <input v-model="bonusPocket.maxVisibleRows" type="number" class="form-control" min="5" max="100"
                   title="Maximum number of rows to display before scrolling" />
          </div>
        </div>

        <button id="calculate-bonus-pocket-btn" type="submit" class="btn btn-primary">Calculate</button>
      </form>

      <!-- Daily Interest Breakdown (similar to normal interest) -->
      <div v-if="bonusPocketResult.dailyBreakdown !== null" class="mb-4">
        <h5>Daily Interest Breakdown</h5>
        <table class="table table-bordered">
          <thead>
          <tr>
            <th>Step</th>
            <th>Base Rate ({{bonusPocket.baseRate}}%)</th>
            <th>Bonus Rate ({{bonusPocket.bonusRate}}%)</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td>1. Calculate daily interest rate (28 significant figures)</td>
            <td>{{ bonusPocketResult.dailyBreakdown.baseDailyRate }}</td>
            <td>{{ bonusPocketResult.dailyBreakdown.bonusDailyRate }}</td>
          </tr>
          <tr>
            <td>2. Calculate daily interest</td>
            <td>{{ bonusPocketResult.dailyBreakdown.baseDailyRate }} × {{ bonusPocket.principal }} = <span class="badge bg-primary">{{ bonusPocketResult.dailyBreakdown.baseDailyInterest }}</span></td>
            <td>{{ bonusPocketResult.dailyBreakdown.bonusDailyRate }} × {{ bonusPocket.principal }} = <span class="badge bg-success">{{ bonusPocketResult.dailyBreakdown.bonusDailyInterest }}</span></td>
          </tr>
          <tr>
            <td>3. Truncate to 5 decimal points</td>
            <td><span class="badge bg-primary">{{ bonusPocketResult.dailyBreakdown.baseDailyInterestTruncated }}</span></td>
            <td><span class="badge bg-success">{{ bonusPocketResult.dailyBreakdown.bonusDailyInterestTruncated }}</span></td>
          </tr>
          <tr>
            <td>4. Round half up to 2 decimal points</td>
            <td><span class="badge bg-primary">{{ bonusPocketResult.dailyBreakdown.baseDailyInterestRounded }}</span></td>
            <td><span class="badge bg-success">{{ bonusPocketResult.dailyBreakdown.bonusDailyInterestRounded }}</span></td>
          </tr>
          </tbody>
        </table>
      </div>

      <!-- Tenor Interest Projection Table -->
      <div v-if="bonusPocketResult.projectionTable !== null" class="mb-4">
        <h5>Interest Projection for {{ calculatedTenor }} Days ({{ formatDate()(bonusPocket.startDate) }} to {{ formatDate()(bonusPocket.endDate) }})</h5>

        <!-- Scrollable table container when rows exceed maxVisibleRows -->
        <div class="table-responsive" :class="{ 'bonus-pocket-scrollable': shouldUseScrollableTable }">
          <div v-if="shouldUseScrollableTable" class="bonus-pocket-table-container" :style="scrollableTableStyle">
            <table class="table table-striped table-bordered mb-0" id="bonus-pocket-projection-table">
              <thead class="sticky-top bg-white">
              <tr>
                <th>Day</th>
                <th>Principal</th>
                <th>Base Interest</th>
                <th>Accrued Bonus Interest</th>
                <th>Cumulated Base Interest</th>
                <th>Cumulated Accrued Bonus Interest</th>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(row, index) in bonusPocketResult.projectionTable" :key="index">
                <td>{{ row.day }}</td>
                <td>{{ row.principal }}</td>
                <td><span class="badge bg-primary">{{ row.baseInterest }}</span></td>
                <td><span class="badge bg-success">{{ row.bonusInterest }}</span></td>
                <td><span class="badge bg-info">{{ row.cumulatedBaseInterest }}</span></td>
                <td><span class="badge bg-warning text-dark">{{ row.cumulatedBonusInterest }}</span></td>
              </tr>
              </tbody>
            </table>
          </div>

          <!-- Non-scrollable table for smaller datasets -->
          <table v-else class="table table-striped table-bordered" id="bonus-pocket-projection-table">
            <thead>
            <tr>
              <th>Day</th>
              <th>Principal</th>
              <th>Base Interest</th>
              <th>Accrued Bonus Interest</th>
              <th>Cumulated Base Interest</th>
              <th>Cumulated Bonus Interest</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(row, index) in bonusPocketResult.projectionTable" :key="index">
              <td>{{ row.day }}</td>
              <td>{{ row.principal }}</td>
              <td><span class="badge bg-primary">{{ row.baseInterest }}</span></td>
              <td><span class="badge bg-success">{{ row.bonusInterest }}</span></td>
              <td><span class="badge bg-info">{{ row.cumulatedBaseInterest }}</span></td>
              <td><span class="badge bg-warning text-dark">{{ row.cumulatedBonusInterest }}</span></td>
            </tr>
            </tbody>
          </table>
        </div>
        <!-- Last day payout formula       -->
        <div class="mt-3">
          <span class="badge" style="background: rgba(113,146,171,0.7);">Final payout</span> = <span class="badge" style="background: rgba(178,152,126,0.7);">last compounded principal</span> + <span class="badge" style="background: rgba(141,114,173,0.7);">last day base interest</span> + <span class="badge" style="background: rgba(138,166,94,0.7);">total accrued bonus interest</span>
        </div>
        <div class="mt-3">
          <span class="badge" style="background: rgba(113,146,171,0.7);">{{ bonusPocketResult.finalAmount }}</span> = <span class="badge" style="background: rgba(178,152,126,0.7);">{{ bonusPocketResult.projectionTable[bonusPocketResult.projectionTable.length - 1].principal }}</span> + <span class="badge" style="background: rgba(141,114,173,0.7);">{{ bonusPocketResult.projectionTable[bonusPocketResult.projectionTable.length - 1].baseInterest }}</span> + <span class="badge" style="background: rgba(138,166,94,0.7);">{{ bonusPocketResult.totalBonusInterest }}</span>
        </div>

        <div class="mt-3">
          <div class="alert alert-info">
            <h6>Summary:</h6>
            <p><strong>Start Date:</strong> {{ formatDate()(bonusPocket.startDate) }}</p>
            <p><strong>End Date:</strong> {{ formatDate()(bonusPocket.endDate) }}</p>
            <p><strong>Total Tenor:</strong> {{ calculatedTenor }} days</p>
            <p><strong>Initial Principal:</strong> {{ bonusPocket.formattedPrincipal }}</p>
            <p><strong>Total Base Interest:</strong> <span class="badge bg-primary">{{ bonusPocketResult.totalBaseInterest }}</span></p>
            <p><strong>Total Bonus Interest:</strong> <span class="badge bg-success">{{ bonusPocketResult.totalBonusInterest }}</span></p>
            <p><strong>Total Interest:</strong> <span class="badge bg-info">{{ bonusPocketResult.totalInterest }}</span></p>
            <p><strong>Final Amount:</strong> <span class="badge bg-secondary">{{ bonusPocketResult.finalAmount }}</span></p>
          </div>
        </div>

        <button type="button" class="btn btn-primary" @click="copyBonusPocketTable()">Copy Table</button>
      </div>
    </div>

    <div v-if="activeTab === 'normal'" class="tab-content mt-3 p-3 rounded bg-light">
      <form @submit.prevent="calculateInterest" class="mb-3">
        <div class="mb-3">
          <label class="form-label">Balance:</label>
          <input id="normal_input_bal" v-model="balance" type="text" class="form-control" pattern="^\d+(\.\d{1,2})?$" title="Should be numeric input up to 2 decimal" required />
        </div>
        <div class="mb-3">
          <label class="form-label">Normal Interest Rate:</label>
          <input v-model="interestRate" type="text" class="form-control" pattern="^\d+(\.\d{1,2})?$" required title="Should be numeric input up to 2 decimal" />
        </div>
        <div class="mb-3">
          <label class="form-label">Days in Year:</label>
          <input v-model.number="daysInYear" type="number" class="form-control" required />
        </div>
        <button id="calculate-normal-btn" type="submit" class="btn btn-primary">Calculate</button>
      </form>

      <div v-if="result !== null">
        <table class="table">
          <thead>
          <tr>
            <th>Step</th>
            <th>{{interestRate}}%</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td>1. count daily interest rate and truncate into 28 significant figure</td>
            <td>{{ dailyInterestRate }}</td>
          </tr>
          <tr>
            <td>2. calculate the interest</td>
            <td>{{ dailyInterestRate }} * {{ balance }}(spot balance) = {{calculatedInterest}}</td>
          </tr>
          <tr>
            <td>3. truncate the interest into 5 decimal points</td>
            <td>{{ truncatedInterest }}</td>
          </tr>
          <tr>
            <td>4. round half up to 2 decimal points</td>
            <td>{{ result }}</td>
          </tr>
          <tr>
            <td>Results</td>
            <td>{{ result }}</td>
          </tr>
          </tbody>
        </table>
        <button type="button" class="btn btn-primary" @click="copyTable('interest-table')">Copy Table</button>
      </div>

    </div>

    <hr>

    <div class="mt-4 p-3 border rounded bg-light">
      <h4>Calculator</h4>
      <div class="row align-items-center">
        <div class="col-auto">
          <input type="text" class="form-control" v-model="calcA" placeholder="1" pattern="^\d+(\.\d+)?$" title="Should be numeric input up to 2 decimal" required>
        </div>
        <div class="col-auto">
          <select class="form-select" v-model="calcOperator">
            <option>+</option>
            <option>-</option>
            <option>X</option>
            <option>/</option>
            <option>trunc</option>
            <option>round</option>
          </select>
        </div>
        <div class="col-auto">
          <input type="text" class="form-control" v-model="calcB" placeholder="2" pattern="^\d+(\.\d+)?$" title="Should be numeric input up to 2 decimal" required>
        </div>
        <div class="col-auto">
          <p>=</p>
        </div>
        <div class="col-auto">
          <p>{{ calcResult }}</p>
        </div>
        <div class="col-auto">
          <button type="button" class="btn btn-primary" @click="performCalculation">Calculate</button>
        </div>
      </div>
    </div>
  </div>

</div>

<script>
  Decimal.set({ precision: 28 });
  new Vue({
    el: "#app",
    data() {
      return {
        activeTab: 'normal',
        balance: '182.49',
        interestRate: '2',
        daysInYear: 365,
        dailyInterestRate: null,
        calculatedInterest: null,
        truncatedInterest: null,

        rayaBalance: '6000',
        campaignThreshold: '5000',
        rayaDaysInYear: 366,
        normalInterestRate: '3',
        rayaInterestRate: '5',
        normalDailyRate: null,
        rayaDailyRate: null,
        rayaResult: {
          balT1: null,
          balT2: null,

          normalDailyRate: null,
          rayaDailyRate: null,
          normalDailyAmountT1: null,
          normalDailyAmountT1Rounded: null,
          normalDailyAmountT1Truncated: null,
          normalDailyAmountT2: null,
          normalDailyAmountT2Rounded: null,
          normalDailyAmountT2Truncated: null,
          rayaDailyAmount: null,
          rayaDailyAmountTruncated: null,
          rayaDailyAmountRounded: null,

          bonusDailyAmount: null,
          normalDailyAmount: null,
          presence: null
        },

        result: null,
        calcA: '',
        calcB: '',
        calcOperator: '+',
        calcResult: '',

        // Bonus Pocket data
        bonusPocket: {
          principal: '500',
          formattedPrincipal: null,
          startDate: '2024-01-01',
          endDate: '2024-01-30',
          baseRate: '2.00',
          bonusRate: '0.48',
          daysInYear: 365,
          maxVisibleRows: 15
        },
        dateValidationError: '',
        bonusPocketResult: {
          dailyBreakdown: null,
          projectionTable: null,
          totalBaseInterest: null,
          totalBonusInterest: null,
          totalInterest: null,
          finalAmount: null
        }
      };
    },
    computed: {
      /**
       * Determine if the projection table should use scrollable container
       */
      shouldUseScrollableTable() {
        if (this.bonusPocket.maxVisibleRows === null || this.bonusPocket.maxVisibleRows === '' || this.bonusPocket.maxVisibleRows <= 0) return false;
        return this.bonusPocketResult.projectionTable !== null &&
               this.bonusPocketResult.projectionTable.length > parseInt(this.bonusPocket.maxVisibleRows);
      },

      /**
       * Calculate the dynamic height for scrollable table container
       */
      scrollableTableStyle() {
        if (!this.shouldUseScrollableTable) return {};

        // Calculate approximate height: header (50px) + (maxVisibleRows * 45px per row) + padding
        const headerHeight = 50;
        const rowHeight = 45;
        const padding = 10;
        const maxVisibleRows = parseInt(this.bonusPocket.maxVisibleRows) || 15;
        const calculatedHeight = headerHeight + (maxVisibleRows * rowHeight) + padding;

        return {
          'max-height': `${calculatedHeight}px`
        };
      },

      /**
       * Calculate tenor in days from start and end dates
       */
      calculatedTenor() {
        if (!this.bonusPocket.startDate || !this.bonusPocket.endDate) {
          return 0;
        }

        const startDate = new Date(this.bonusPocket.startDate);
        const endDate = new Date(this.bonusPocket.endDate);

        if (endDate <= startDate) {
          return 0;
        }

        // Calculate difference in days (inclusive of both start and end dates)
        const timeDifference = endDate.getTime() - startDate.getTime();
        const daysDifference = Math.ceil(timeDifference / (1000 * 3600 * 24)) + 1;

        return daysDifference;
      },
    },
    methods: {

      /**
       * Format date for display in summary
       */
      formatDate() {
        return (dateString) => {
          if (!dateString) return '';
          const date = new Date(dateString);
          return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
        };
      },
      calculateInterest() {
        const bal = new Decimal(this.balance);
        const rate = new Decimal(this.interestRate);
        const days = new Decimal(this.daysInYear);

        const dailyRate = rate.dividedBy(days).dividedBy(100);
        this.dailyInterestRate = dailyRate.toPrecision(28);

        let interest = bal.times(dailyRate);
        this.calculatedInterest = interest.toPrecision(28);

        interest = interest.toDP(5, Decimal.ROUND_DOWN);
        this.truncatedInterest = interest;

        this.result = interest.toDP(2, Decimal.ROUND_HALF_UP);
      },
      performCalculation() {
        let a = new Decimal(this.calcA || 0);
        let b = new Decimal(this.calcB || 0);
        let result;
        switch (this.calcOperator) {
          case '+': result = a.plus(b); break;
          case '-': result = a.minus(b); break;
          case 'X': result = a.times(b); break;
          case '/': result = a.dividedBy(b); break;
          case 'trunc': result = a.toDP(parseInt(b), Decimal.ROUND_DOWN); break;
          case 'round': result = a.toDP(parseInt(b), Decimal.ROUND_HALF_UP); break;
        }
        this.calcResult = result;
      },
      calculateRayaInterest() {
        const bal = new Decimal(this.rayaBalance);
        const normalRate = new Decimal(this.normalInterestRate);
        const rayaRate = new Decimal(this.rayaInterestRate);
        const days = new Decimal(this.rayaDaysInYear);
        const threshold = new Decimal(this.campaignThreshold);

        this.normalDailyRate = normalRate.dividedBy(days).dividedBy(100);
        this.rayaDailyRate = rayaRate.dividedBy(days).dividedBy(100);
        const balT1 = bal.lessThanOrEqualTo(threshold) ? bal : threshold;
        let balT2 = bal.minus(threshold);
        if (balT2.isNeg()) balT2 = new Decimal(0);
        this.rayaResult.normalDailyRate = this.normalDailyRate;
        this.rayaResult.rayaDailyRate = this.rayaDailyRate;

        this.rayaResult.balT1 = balT1;
        this.rayaResult.balT2 = balT2;

        let normalDailyAmountT1 = balT1.times(this.normalDailyRate);
        let normalDailyAmountT2 = balT2.times(this.normalDailyRate);
        let rayaDailyAmount = balT1.times(this.rayaDailyRate);

        this.rayaResult.normalDailyAmountT1 = normalDailyAmountT1;
        this.rayaResult.normalDailyAmountT2 = normalDailyAmountT2;
        this.rayaResult.rayaDailyAmount = rayaDailyAmount;

        normalDailyAmountT1 = normalDailyAmountT1.toDP(5, Decimal.ROUND_DOWN);
        normalDailyAmountT2 = normalDailyAmountT2.toDP(5, Decimal.ROUND_DOWN);
        rayaDailyAmount = rayaDailyAmount.toDP(5, Decimal.ROUND_DOWN);
        this.rayaResult.normalDailyAmountT1Truncated = normalDailyAmountT1;
        this.rayaResult.normalDailyAmountT2Truncated = normalDailyAmountT2;
        this.rayaResult.rayaDailyAmountTruncated = rayaDailyAmount;

        normalDailyAmountT1 = normalDailyAmountT1.toDP(2, Decimal.ROUND_HALF_UP);
        normalDailyAmountT2 = normalDailyAmountT2.toDP(2, Decimal.ROUND_HALF_UP);
        rayaDailyAmount = rayaDailyAmount.toDP(2, Decimal.ROUND_HALF_UP);
        this.rayaResult.normalDailyAmountT1Rounded = normalDailyAmountT1;
        this.rayaResult.normalDailyAmountT2Rounded = normalDailyAmountT2;
        this.rayaResult.rayaDailyAmountRounded = rayaDailyAmount;

        this.rayaResult.bonusDailyAmount = rayaDailyAmount.minus(normalDailyAmountT1);
        this.rayaResult.normalDailyAmount = normalDailyAmountT1.add(normalDailyAmountT2);
        this.rayaResult.presence = true;
      },
      formatToK(decimalValue) {
        const value = new Decimal(decimalValue);
        return value.dividedBy(1000).toString() + 'k';
      },
      copyTable(tableId) {
        const table = document.getElementById(tableId);
        if (!table) {
          console.error('Table not found:', tableId);
          return;
        }
        const range = document.createRange();
        range.selectNode(table);
        window.getSelection().removeAllRanges();
        window.getSelection().addRange(range);
        document.execCommand('copy');
        window.getSelection().removeAllRanges();
      },

      /**
       * Copy the bonus pocket projection table including the final summary row
       */
      copyBonusPocketTable() {
        if (!this.bonusPocketResult.projectionTable) {
          console.error('No projection table data to copy');
          return;
        }

        // Create a comprehensive table data string
        let tableData = 'Day\tPrincipal\tBase Interest\tAccrued Bonus Interest\tTotal Daily Interest\n';

        // Add all projection rows
        this.bonusPocketResult.projectionTable.forEach(row => {
          tableData += `${row.day}\t${row.principal}\t${row.baseInterest}\t${row.bonusInterest}\t${row.totalInterest}\n`;
        });

        // Add final summary row
        tableData += `Final\t${this.bonusPocketResult.finalAmount}\t-\t${this.bonusPocketResult.totalBonusInterest}\t${this.bonusPocketResult.totalInterest}\n`;

        // Copy to clipboard using modern API if available, fallback to legacy method
        if (navigator.clipboard && window.isSecureContext) {
          navigator.clipboard.writeText(tableData).then(() => {
            console.log('Table data copied to clipboard');
          }).catch(err => {
            console.error('Failed to copy table data:', err);
            this.fallbackCopyToClipboard(tableData);
          });
        } else {
          this.fallbackCopyToClipboard(tableData);
        }
      },

      /**
       * Fallback method for copying text to clipboard
       */
      fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
          document.execCommand('copy');
          console.log('Table data copied to clipboard (fallback method)');
        } catch (err) {
          console.error('Failed to copy table data:', err);
        }

        document.body.removeChild(textArea);
      },

      /**
       * Validate date range inputs
       */
      validateDateRange() {
        this.dateValidationError = '';

        if (!this.bonusPocket.startDate || !this.bonusPocket.endDate) {
          return;
        }

        const startDate = new Date(this.bonusPocket.startDate);
        const endDate = new Date(this.bonusPocket.endDate);

        if (endDate <= startDate) {
          this.dateValidationError = 'End date must be after start date.';
          return;
        }

        if (this.calculatedTenor > 365) {
          this.dateValidationError = 'Date range cannot exceed 365 days.';
          return;
        }
      },

      /**
       * Calculate Bonus Pocket Interest
       * This method calculates both daily breakdown and tenor projection
       */
      calculateBonusPocketInterest() {
        // Validate dates first
        this.validateDateRange();
        if (this.dateValidationError) {
          return;
        }

        // Parse input values
        const principal = new Decimal(this.bonusPocket.principal);
        const tenor = this.calculatedTenor;
        const baseRate = new Decimal(this.bonusPocket.baseRate);
        const bonusRate = new Decimal(this.bonusPocket.bonusRate);
        const daysInYear = new Decimal(this.bonusPocket.daysInYear);

        if (tenor <= 0) {
          this.dateValidationError = 'Please select valid start and end dates.';
          return;
        }

        // Calculate daily rates (28 significant figures precision)
        const baseDailyRate = baseRate.dividedBy(daysInYear).dividedBy(100);
        const bonusDailyRate = bonusRate.dividedBy(daysInYear).dividedBy(100);

        // Daily breakdown calculation (similar to normal interest)
        const baseDailyInterest = principal.times(baseDailyRate);
        const bonusDailyInterest = principal.times(bonusDailyRate);

        // Truncate to 5 decimal points
        const baseDailyInterestTruncated = baseDailyInterest.toDP(5, Decimal.ROUND_DOWN);
        const bonusDailyInterestTruncated = bonusDailyInterest.toDP(5, Decimal.ROUND_DOWN);

        // Round to 2 decimal points
        const baseDailyInterestRounded = baseDailyInterestTruncated.toDP(2, Decimal.ROUND_HALF_UP);
        const bonusDailyInterestRounded = bonusDailyInterestTruncated.toDP(2, Decimal.ROUND_HALF_UP);

        // Store daily breakdown results
        this.bonusPocketResult.dailyBreakdown = {
          baseDailyRate: baseDailyRate.toPrecision(28),
          bonusDailyRate: bonusDailyRate.toPrecision(28),
          baseDailyInterest: baseDailyInterest.toPrecision(28),
          bonusDailyInterest: bonusDailyInterest.toPrecision(28),
          baseDailyInterestTruncated: baseDailyInterestTruncated.toString(),
          bonusDailyInterestTruncated: bonusDailyInterestTruncated.toString(),
          baseDailyInterestRounded: baseDailyInterestRounded.toString(),
          bonusDailyInterestRounded: bonusDailyInterestRounded.toString()
        };

        // Generate projection table
        this.generateBonusPocketProjection(principal, tenor, baseDailyRate, bonusDailyRate);
      },

      /**
       * Generate the tenor projection table for Bonus Pocket
       * @param {Decimal} initialPrincipal - Starting principal amount
       * @param {number} tenor - Number of days
       * @param {Decimal} baseDailyRate - Daily base interest rate
       * @param {Decimal} bonusDailyRate - Daily bonus interest rate
       */
      generateBonusPocketProjection(initialPrincipal, tenor, baseDailyRate, bonusDailyRate) {
        const projectionTable = [];
        let currentPrincipal = initialPrincipal;
        let totalBaseInterest = new Decimal(0);
        let totalBonusInterest = new Decimal(0);

        // Calculate for each day
        for (let day = 1; day <= tenor; day++) {
          // Calculate daily interests based on current principal
          const dailyBaseInterest = currentPrincipal.times(baseDailyRate);
          const dailyBonusInterest = currentPrincipal.times(bonusDailyRate);

          // Apply truncation and rounding as per standard calculation
          const dailyBaseInterestProcessed = dailyBaseInterest.toDP(5, Decimal.ROUND_DOWN).toDP(2, Decimal.ROUND_HALF_UP);
          const dailyBonusInterestProcessed = dailyBonusInterest.toDP(5, Decimal.ROUND_DOWN).toDP(2, Decimal.ROUND_HALF_UP);

          const totalDailyInterest = dailyBaseInterestProcessed.plus(dailyBonusInterestProcessed);

          // Add row to projection table
          projectionTable.push({
            day: day,
            principal: currentPrincipal.toDP(2, Decimal.ROUND_HALF_UP).toFixed(2).toString(),
            baseInterest: dailyBaseInterestProcessed.toFixed(2).toString(),
            bonusInterest: dailyBonusInterestProcessed.toFixed(2).toString(),
            totalInterest: totalDailyInterest.toFixed(2).toString()
          });

          // Update totals
          totalBaseInterest = totalBaseInterest.plus(dailyBaseInterestProcessed);
          totalBonusInterest = totalBonusInterest.plus(dailyBonusInterestProcessed);

          // For next day: principal = current principal + base interest
          // (bonus interest is accrued separately)
          currentPrincipal = currentPrincipal.plus(dailyBaseInterestProcessed);
        }
        const isDecimal = function (obj) {
          return obj instanceof Decimal;
        };

        // compute daily cumulated base and bonus interest
        for (let i = 0; i < projectionTable.length; i++) {
          let prevRow = i === 0 ? {cumulatedBaseInterest: new Decimal(0), cumulatedBonusInterest: new Decimal(0)} : projectionTable[i-1];

          prevCumulatedBaseInterest = isDecimal(prevRow.cumulatedBaseInterest) ? prevRow.cumulatedBaseInterest : new Decimal(prevRow.cumulatedBaseInterest);
          prevCumulatedBonusInterest = isDecimal(prevRow.cumulatedBonusInterest) ? prevRow.cumulatedBonusInterest : new Decimal(prevRow.cumulatedBonusInterest);

          projectionTable[i].cumulatedBaseInterest = prevCumulatedBaseInterest.plus(
                  new Decimal(projectionTable[i].baseInterest)
          ).toFixed(2).toString();
          projectionTable[i].cumulatedBonusInterest = prevCumulatedBonusInterest.plus(
                  new Decimal(projectionTable[i].bonusInterest)
          ).toFixed(2).toString();
        }

        // Calculate final amounts
        const totalInterest = totalBaseInterest.plus(totalBonusInterest);
        const finalAmount = initialPrincipal.plus(totalBaseInterest).plus(totalBonusInterest);

        // Store results
        this.bonusPocketResult.projectionTable = projectionTable;
        this.bonusPocketResult.totalBaseInterest = totalBaseInterest.toDP(2, Decimal.ROUND_HALF_UP).toFixed(2).toString();
        this.bonusPocketResult.totalBonusInterest = totalBonusInterest.toDP(2, Decimal.ROUND_HALF_UP).toFixed(2).toString();
        this.bonusPocketResult.totalInterest = totalInterest.toDP(2, Decimal.ROUND_HALF_UP).toFixed(2).toString();
        this.bonusPocketResult.finalAmount = finalAmount.toDP(2, Decimal.ROUND_HALF_UP).toFixed(2).toString();
        this.bonusPocket.formattedPrincipal = (new Decimal(this.bonusPocket.principal)).toFixed(2).toString();
      }
    }
  });
</script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
