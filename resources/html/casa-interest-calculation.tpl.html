<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GX Bank Interest Calculation (V1.4)</title>
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/decimal.js/9.0.0/decimal.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body class="container mt-4">
<div id="app">
  <h1 class="text-center mb-4">GX Bank Interest Calculation (V1.5)</h1>

  <div class="container">
    <div class="accordion" id="accordionSection">
      <div class="accordion-item">
        <h2 class="accordion-header" id="headingOne">
          <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#readme-content" aria-expanded="true" aria-controls="collapseOne">
            Readme
          </button>
        </h2>
        <div id="readme-content" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionSection">
          <div class="accordion-body">
            <ul>
              <li>
                This simulator is available for download <a href="https://gxbank.atlassian.net/wiki/spaces/CE/pages/*********/Thought+Machine+Interest+Calculation" target="_blank">here.</a> Its a self contained html page that so long as you have a modern browser and internet connection, you can use it directly without any dependency.
              </li>
              <li>
                The simulator uses <a href="https://speleotrove.com/decimal/decarith.html" target="_blank">decimal arithmetic</a> to perform the computation and precision being used is <strong>28 significant digit</strong> unless otherwise specified. <b><a href="https://en.wikipedia.org/wiki/Significant_figures" target="_blank">Significant digit precision</a> is not same as decimal precision!</b>
              </li>
              <li>
                The simulator is tested based on these <a href="https://docs.google.com/spreadsheets/d/1gFuaUhSc0f3Ya0wyV3KJEyHs98UPgCyBvo0xY2xkYrE/edit#gid=0" target="_blank">test cases</a>. The test results are
                <a href="https://drive.google.com/drive/folders/1LtqnZGjNqXmWGnVR1KNcEBH13EOlPb0X?usp=drive_link" target="_blank">here</a>. Reach out to
                <a href="https://gxbank.slack.com/archives/C07FZ86APCY">Core Banking team</a> should you have query on the test results or simulation results.
              </li>
            </ul>
            <div class="alert alert-danger" role="alert">
              <p>
                This simulator is for reference only. The actual interest calculation is done by the Core Banking system.
              </p>
              <p>
                This simulator is meant for internal use and should not be shared with external parties!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <hr>

  <div class="container">

    <ul class="nav nav-tabs">
      <li class="nav-item">
        <a id="normal-tab-link" class="nav-link" :class="{ active: activeTab === 'normal' }" @click="activeTab = 'normal'" href="#">Normal Interest Breakdown</a>
      </li>
      <li class="nav-item">
        <a id="raya-tab-link" class="nav-link" :class="{ active: activeTab === 'raya' }" @click="activeTab = 'raya'" href="#">Raya Campaign</a>
      </li>
    </ul>

    <div v-if="activeTab === 'raya'" class="tab-content mt-3 p-3 rounded bg-light">
      <h4>Raya Campaign Interest Breakdown</h4>
      <form @submit.prevent="calculateRayaInterest" class="mb-3">
        <div class="mb-3 row">
          <div class="col">
            <label class="form-label">Balance:</label>
            <input id="raya-balance-input" v-model="rayaBalance" type="text" class="form-control" required />
          </div>
          <div class="col">
            <label class="form-label">Campaign Threshold:</label>
            <input v-model="campaignThreshold" type="text" class="form-control" disabled
                   pattern="^\d+(\.\d{1,2})?$" title="Should be numeric input up to 2 decimal" required
            />
          </div>
        </div>

        <div class="mb-3">
          <label class="form-label">Normal Interest Rate:</label>
          <input v-model="normalInterestRate" type="text" class="form-control" pattern="^\d+(\.\d{1,2})?$" title="Should be numeric input up to 2 decimal" required />
        </div>


        <div class="mb-3">
          <label class="form-label">Raya Interest Rate:</label>
          <input v-model="rayaInterestRate" type="text" class="form-control" pattern="^\d+(\.\d{1,2})?$" title="Should be numeric input up to 2 decimal" required />
        </div>
        <div class="mb-3">
          <label class="form-label">Days in year</label>
          <input v-model="rayaDaysInYear" type="text" class="form-control" pattern="^\d+" title="Should be integer only" required />
        </div>
        <button id="calculate-raya-btn" type="submit" class="btn btn-primary">Calculate</button>
      </form>
      <div v-if="rayaResult !== null && rayaResult.presence">
        <div id="raya-summary">
          <p class="lead">Customer earned <strong>{{rayaResult.rayaDailyAmountRounded.add(rayaResult.normalDailyAmountT2Rounded)}}</strong> daily total interest (<strong>{{rayaResult.balT2.isZero() ? rayaResult.normalDailyAmountT1Rounded : rayaResult.normalDailyAmountT1Rounded + " + " + rayaResult.normalDailyAmountT2Rounded + " = " + rayaResult.normalDailyAmount}}</strong> daily normal interest, <strong>{{rayaResult.bonusDailyAmount}}</strong> daily bonus interest.</p>
        </div>
        <table class="table" id="raya-table">
          <thead>
          <tr>
            <th>Steps</th>
            <th id="raya-col-header-5perc">5%<p>bal: {{rayaResult.balT1}}</p></th>
            <th id="raya-col-header-3perc-within">3% (within {{formatToK(campaignThreshold)}})<p>bal: {{rayaResult.balT1}}</p></th>
            <th id="raya-col-header-3perc-beyond">3% (beyond {{formatToK(campaignThreshold)}})<p>{{rayaResult.balT2}}</p></th>
            <th>2% (campaign)</th>
          </tr>
          </thead>

          <tbody>
          <tr>
            <td>1. count daily interest rate and truncate into 28 significant figure
              <precision></precision>
            </td>
            <td data-steps="step_1_total">{{rayaResult.rayaDailyRate}}</td>
            <td data-steps="step_1_normal">{{rayaResult.normalDailyRate}}</td>
            <td data-steps="step_1_normal_beyond"></td>
            <td data-steps="step_1_bonus">-</td>
          </tr>
          <tr>
            <td>2. calculate the interest</td>
            <td data-steps="step_2_total">{{rayaResult.rayaDailyRate}} * {{rayaResult.balT1}}(balance within threshold) = {{rayaResult.rayaDailyAmount}}</td>
            <td data-steps="step_2_normal">{{rayaResult.normalDailyRate}} * {{rayaResult.balT1}}(balance within threshold) = {{rayaResult.normalDailyAmountT1}}</td>
            <td data-steps="step_2_normal_beyond">
              {{rayaResult.balT2.isZero() ? '' : rayaResult.normalDailyRate}} * {{rayaResult.balT2}}(balance beyond threshold) = {{rayaResult.normalDailyAmountT2}}
            </td>
            <td data-steps="step_2_bonus">-</td>
          </tr>
          <tr>
            <td>3. truncate the interest into 5 decimal points</td>
            <td data-steps="step_3_total">{{rayaResult.rayaDailyAmountTruncated}}</td>
            <td data-steps="step_3_normal">{{rayaResult.normalDailyAmountTruncated}}</td>
            <td data-steps="step_3_normal_beyond">
              {{rayaResult.balT2.isZero() ? '' : rayaResult.normalDailyAmountT2Truncated}}
            </td>
            <td data-steps="step_3_bonus">-</td>
          </tr>
          <tr>
            <td>4. round half up to 2 decimal points</td>
            <td data-steps="step_4_total">
              <p data-bs-toggle="tooltip" data-bs-placement="top"
                 title="Total interest customer received">
                {{rayaResult.rayaDailyAmountRounded}}
              </p></td>
            <td data-steps="step_4_normal">
              {{rayaResult.normalDailyAmountT1Rounded}}
            </td>
            <td data-steps="step_4_normal_beyond">
              {{rayaResult.balT2.isZero() ? '' : rayaResult.normalDailyAmountT2Rounded}}
            </td>
            <td data-steps="step_4_bonus">-</td>
          </tr>
          <tr>
            <td>5. calculate the interest for campaign interest</td>
            <td data-steps="step_5_total"></td>
            <td data-steps="step_5_normal"></td>
            <td data-steps="step_5_normal_beyond"></td>
            <td data-steps="step_5_bonus">
              {{rayaResult.rayaDailyAmountRounded}}({{rayaRate}} %) - {{rayaResult.normalDailyAmountT1Rounded}}(within {{formatToK(campaignThreshold)}}) = {{rayaResult.bonusDailyAmount}}
            </td>
          </tr>
          <tr>
            <td>Results</td>
            <td data-steps="step_6_total"></td>
            <td data-steps="step_6_normal">{{rayaResult.normalDailyAmountT1Rounded}}</td>
            <td data-steps="step_6_normal_beyond">
              {{rayaResult.balT2.isZero() ? '' : rayaResult.normalDailyAmountT2Rounded}}
            </td>
            <td data-steps="step_6_bonus">{{rayaResult.bonusDailyAmount}}</td>
          </tr>
          </tbody>
        </table>

        <button type="button" class="btn btn-primary" @click="copyTable('raya-table')">Copy Table</button>
      </div>
    </div>

    <div v-if="activeTab === 'normal'" class="tab-content mt-3 p-3 rounded bg-light">
      <form @submit.prevent="calculateInterest" class="mb-3">
        <div class="mb-3">
          <label class="form-label">Balance:</label>
          <input id="normal_input_bal" v-model="balance" type="text" class="form-control" pattern="^\d+(\.\d{1,2})?$" title="Should be numeric input up to 2 decimal" required />
        </div>
        <div class="mb-3">
          <label class="form-label">Normal Interest Rate:</label>
          <input v-model="interestRate" type="text" class="form-control" pattern="^\d+(\.\d{1,2})?$" required title="Should be numeric input up to 2 decimal" />
        </div>
        <div class="mb-3">
          <label class="form-label">Days in Year:</label>
          <input v-model.number="daysInYear" type="number" class="form-control" required />
        </div>
        <button id="calculate-normal-btn" type="submit" class="btn btn-primary">Calculate</button>
      </form>

      <div v-if="result !== null">
        <table class="table">
          <thead>
          <tr>
            <th>Step</th>
            <th>{{interestRate}}%</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <td>1. count daily interest rate and truncate into 28 significant figure</td>
            <td>{{ dailyInterestRate }}</td>
          </tr>
          <tr>
            <td>2. calculate the interest</td>
            <td>{{ dailyInterestRate }} * {{ balance }}(spot balance) = {{calculatedInterest}}</td>
          </tr>
          <tr>
            <td>3. truncate the interest into 5 decimal points</td>
            <td>{{ truncatedInterest }}</td>
          </tr>
          <tr>
            <td>4. round half up to 2 decimal points</td>
            <td>{{ result }}</td>
          </tr>
          <tr>
            <td>Results</td>
            <td>{{ result }}</td>
          </tr>
          </tbody>
        </table>
        <button type="button" class="btn btn-primary" @click="copyTable('interest-table')">Copy Table</button>
      </div>

    </div>

    <hr>

    <div class="mt-4 p-3 border rounded bg-light">
      <h4>Calculator</h4>
      <div class="row align-items-center">
        <div class="col-auto">
          <input type="text" class="form-control" v-model="calcA" placeholder="1" pattern="^\d+(\.\d+)?$" title="Should be numeric input up to 2 decimal" required>
        </div>
        <div class="col-auto">
          <select class="form-select" v-model="calcOperator">
            <option>+</option>
            <option>-</option>
            <option>X</option>
            <option>/</option>
            <option>trunc</option>
            <option>round</option>
          </select>
        </div>
        <div class="col-auto">
          <input type="text" class="form-control" v-model="calcB" placeholder="2" pattern="^\d+(\.\d+)?$" title="Should be numeric input up to 2 decimal" required>
        </div>
        <div class="col-auto">
          <p>=</p>
        </div>
        <div class="col-auto">
          <p>{{ calcResult }}</p>
        </div>
        <div class="col-auto">
          <button type="button" class="btn btn-primary" @click="performCalculation">Calculate</button>
        </div>
      </div>
    </div>
  </div>

</div>

<script>
  Decimal.set({ precision: 28 });
  new Vue({
    el: "#app",
    data() {
      return {
        activeTab: 'normal',
        balance: '182.49',
        interestRate: '2',
        daysInYear: 365,
        dailyInterestRate: null,
        calculatedInterest: null,
        truncatedInterest: null,

        rayaBalance: '6000',
        campaignThreshold: '5000',
        rayaDaysInYear: 366,
        normalInterestRate: '3',
        rayaInterestRate: '5',
        normalDailyRate: null,
        rayaDailyRate: null,
        rayaResult: {
          balT1: null,
          balT2: null,

          normalDailyRate: null,
          rayaDailyRate: null,
          normalDailyAmountT1: null,
          normalDailyAmountT1Rounded: null,
          normalDailyAmountT1Truncated: null,
          normalDailyAmountT2: null,
          normalDailyAmountT2Rounded: null,
          normalDailyAmountT2Truncated: null,
          rayaDailyAmount: null,
          rayaDailyAmountTruncated: null,
          rayaDailyAmountRounded: null,

          bonusDailyAmount: null,
          normalDailyAmount: null,
          presence: null
        },

        result: null,
        calcA: '',
        calcB: '',
        calcOperator: '+',
        calcResult: ''
      };
    },
    methods: {
      calculateInterest() {
        const bal = new Decimal(this.balance);
        const rate = new Decimal(this.interestRate);
        const days = new Decimal(this.daysInYear);

        const dailyRate = rate.dividedBy(days).dividedBy(100);
        this.dailyInterestRate = dailyRate.toPrecision(28);

        let interest = bal.times(dailyRate);
        this.calculatedInterest = interest.toPrecision(28);

        interest = interest.toDP(5, Decimal.ROUND_DOWN);
        this.truncatedInterest = interest;

        this.result = interest.toDP(2, Decimal.ROUND_HALF_UP);
      },
      performCalculation() {
        let a = new Decimal(this.calcA || 0);
        let b = new Decimal(this.calcB || 0);
        let result;
        switch (this.calcOperator) {
          case '+': result = a.plus(b); break;
          case '-': result = a.minus(b); break;
          case 'X': result = a.times(b); break;
          case '/': result = a.dividedBy(b); break;
          case 'trunc': result = a.toDP(parseInt(b), Decimal.ROUND_DOWN); break;
          case 'round': result = a.toDP(parseInt(b), Decimal.ROUND_HALF_UP); break;
        }
        this.calcResult = result;
      },
      calculateRayaInterest() {
        const bal = new Decimal(this.rayaBalance);
        const normalRate = new Decimal(this.normalInterestRate);
        const rayaRate = new Decimal(this.rayaInterestRate);
        const days = new Decimal(this.rayaDaysInYear);
        const threshold = new Decimal(this.campaignThreshold);

        this.normalDailyRate = normalRate.dividedBy(days).dividedBy(100);
        this.rayaDailyRate = rayaRate.dividedBy(days).dividedBy(100);
        const balT1 = bal.lessThanOrEqualTo(threshold) ? bal : threshold;
        let balT2 = bal.minus(threshold);
        if (balT2.isNeg()) balT2 = new Decimal(0);
        this.rayaResult.normalDailyRate = this.normalDailyRate;
        this.rayaResult.rayaDailyRate = this.rayaDailyRate;

        this.rayaResult.balT1 = balT1;
        this.rayaResult.balT2 = balT2;

        let normalDailyAmountT1 = balT1.times(this.normalDailyRate);
        let normalDailyAmountT2 = balT2.times(this.normalDailyRate);
        let rayaDailyAmount = balT1.times(this.rayaDailyRate);

        this.rayaResult.normalDailyAmountT1 = normalDailyAmountT1;
        this.rayaResult.normalDailyAmountT2 = normalDailyAmountT2;
        this.rayaResult.rayaDailyAmount = rayaDailyAmount;

        normalDailyAmountT1 = normalDailyAmountT1.toDP(5, Decimal.ROUND_DOWN);
        normalDailyAmountT2 = normalDailyAmountT2.toDP(5, Decimal.ROUND_DOWN);
        rayaDailyAmount = rayaDailyAmount.toDP(5, Decimal.ROUND_DOWN);
        this.rayaResult.normalDailyAmountT1Truncated = normalDailyAmountT1;
        this.rayaResult.normalDailyAmountT2Truncated = normalDailyAmountT2;
        this.rayaResult.rayaDailyAmountTruncated = rayaDailyAmount;

        normalDailyAmountT1 = normalDailyAmountT1.toDP(2, Decimal.ROUND_HALF_UP);
        normalDailyAmountT2 = normalDailyAmountT2.toDP(2, Decimal.ROUND_HALF_UP);
        rayaDailyAmount = rayaDailyAmount.toDP(2, Decimal.ROUND_HALF_UP);
        this.rayaResult.normalDailyAmountT1Rounded = normalDailyAmountT1;
        this.rayaResult.normalDailyAmountT2Rounded = normalDailyAmountT2;
        this.rayaResult.rayaDailyAmountRounded = rayaDailyAmount;

        this.rayaResult.bonusDailyAmount = rayaDailyAmount.minus(normalDailyAmountT1);
        this.rayaResult.normalDailyAmount = normalDailyAmountT1.add(normalDailyAmountT2);
        this.rayaResult.presence = true;
      },
      formatToK(decimalValue) {
        const value = new Decimal(decimalValue);
        return value.dividedBy(1000).toString() + 'k';
      },
      copyTable(tableId) {
        const table = document.getElementById(tableId);
        const range = document.createRange();
        range.selectNode(table);
        window.getSelection().removeAllRanges();
        window.getSelection().addRange(range);
        document.execCommand('copy');
        window.getSelection().removeAllRanges();
      }
    }
  });
</script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
