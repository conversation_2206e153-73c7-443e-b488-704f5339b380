<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Tamper Check (v1.5)</title>
    <style>
        /* --- General Styles --- */
        body {
            font-family: sans-serif;
            line-height: 1.6;
            padding: 20px;
            background-color: #f4f4f4;
        }
        #container {
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            max-width: 800px; /* Adjusted for potentially wider table */
            margin: 20px auto;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"] {
            display: block;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: calc(100% - 22px); /* Account for padding and border */
        }
        button#checkButton { /* More specific selector */
            background-color: #007bff;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
            display: block;
            width: 100%;
            margin-bottom: 20px;
        }
        button#checkButton:hover {
            background-color: #0056b3;
        }
        button#checkButton:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        /* --- Summary Banner Styles --- */
        #summary-banner {
            background-color: #e9ecef;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            text-align: center;
            display: none; /* Hidden initially */
        }
        #summary-banner span {
            margin: 0 10px;
            font-weight: bold;
            vertical-align: middle; /* Align text with buttons */
        }
        #summary-banner .filter-btn {
            background-color: #6c757d;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            margin-left: 5px;
            margin-right: 15px;
            transition: background-color 0.2s ease;
            vertical-align: middle; /* Align buttons nicely with text */
        }
        #summary-banner .filter-btn:hover {
            background-color: #5a6268;
        }
        #summary-banner .filter-btn.legit-btn {
            background-color: #28a745;
        }
        #summary-banner .filter-btn.legit-btn:hover {
            background-color: #218838;
        }
         #summary-banner .filter-btn.fake-btn {
            background-color: #dc3545;
        }
        #summary-banner .filter-btn.fake-btn:hover {
            background-color: #c82333;
        }
        #summary-banner .filter-btn:disabled {
             background-color: #ccc;
             cursor: not-allowed;
             opacity: 0.7;
        }

        /* --- Results Area Styles --- */
        #results {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #eee;
            background-color: #f9f9f9;
            border-radius: 4px;
            min-height: 50px;
        }
        /* Styles for List View Items */
        #results-list-view .result-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: block; /* Default state for filtering */
        }
        #results-list-view .result-item:last-child {
            border-bottom: none;
        }
        #results-list-view .result-header { margin-bottom: 5px; }
        #results-list-view .result-header span.filename { font-weight: bold; }
        #results-list-view .status-legit { color: green; font-weight: bold; }
        #results-list-view .status-fake { color: red; font-weight: bold; }
        #results-list-view .summary { font-size: 0.9em; color: #555; margin-left: 10px; }
        #results-list-view .check-details { font-size: 0.85em; margin-left: 15px; margin-top: 5px; }
        #results-list-view .check-details span { display: block; margin-bottom: 2px; }
        #results-list-view .check-passed { color: green; }
        #results-list-view .check-failed { color: red; }
        #results-list-view .note { font-size: 0.8em; font-style: italic; color: #666; margin-left: 10px; }

        /* Styles for Table View */
        #results-table-view {
            overflow-x: auto; /* Allow horizontal scrolling if table is too wide */
        }
        #results-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9em;
            margin-top: 10px;
            table-layout: auto; /* Adjust column widths based on content */
        }
        #results-table thead th {
            background-color: #f2f2f2;
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-weight: bold;
            white-space: nowrap; /* Prevent headers from wrapping */
        }
        #results-table tbody td {
            border: 1px solid #ddd;
            padding: 8px;
            vertical-align: top;
            /* Consider adding max-width and text overflow for filename if needed */
             /* max-width: 150px;
             overflow: hidden;
             text-overflow: ellipsis;
             white-space: nowrap; */
        }
        #results-table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        /* Status indicators within table */
        #results-table .status-legit { color: green; font-weight: bold; }
        #results-table .status-fake { color: red; font-weight: bold; }
        #results-table .check-passed { color: green; }
        #results-table .check-failed { color: red; }
        #results-table .check-error { color: orange; font-weight: bold;} /* For errors */

        /* --- Common Styles & Utilities --- */
        .processing-notice { font-style: italic; color: #555; }
        .warning {
            background-color: #fff3cd; color: #856404; border: 1px solid #ffeeba;
            padding: 10px; border-radius: 4px; margin-bottom: 20px; font-size: 0.9em;
        }
        /* Class to hide elements during filtering (works on list items and table rows) */
        .hidden-result { display: none !important; }

        /* Add new style for download button */
        #downloadCsvBtn {
            background-color: #28a745;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            margin-left: 20px;
            transition: background-color 0.2s ease;
            vertical-align: middle;
            display: none; /* Hidden initially */
        }
        #downloadCsvBtn:hover {
            background-color: #218838;
        }
        #downloadCsvBtn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>

    <div id="container">
        <h1>PDF Tamper Check (v1.5)</h1>

        <div class="warning">
            <strong>Disclaimer:</strong> This tool performs basic text checks on PDF files and is NOT a foolproof method for detecting tampering.
            <br>
            <strong>Status Logic:</strong> Uses <strong>3</strong> checks. A file is marked "FAKE" if <strong>2 or more (&ge;2)</strong> checks fail. Otherwise, it's marked "Legit". Interpret results with caution.
        </div>

        <label for="pdfUpload">Upload PDF Files (Multiple Allowed):</label>
        <input type="file" id="pdfUpload" accept=".pdf" multiple>

        <button id="checkButton">Check Files</button>

        <div id="summary-banner">
            <span>Results Summary:</span>
            <span id="legit-count-display">Legit: 0</span>
            <button id="show-legit" class="filter-btn legit-btn" title="Show only Legit files" disabled>Show Legit</button>
            <span id="fake-count-display">Fake: 0</span>
            <button id="show-fake" class="filter-btn fake-btn" title="Show only FAKE files" disabled>Show Fake</button>
            <button id="show-all" class="filter-btn" title="Show all files" disabled>Show All</button>
            <button id="toggle-view-btn" class="filter-btn" title="Switch View" style="margin-left: 20px;" disabled>Switch to Table View</button>
            <button id="downloadCsvBtn" title="Download results as CSV">Download CSV</button>
        </div>

        <div id="results">
            <div id="results-list-view">
                <p>Results will appear here...</p>
            </div>
            <div id="results-table-view" style="display: none;">
                </div>
        </div>
    </div>

    <script>
        // --- Element References ---
        const pdfUpload = document.getElementById('pdfUpload');
        const checkButton = document.getElementById('checkButton');
        const resultsDiv = document.getElementById('results');
        const resultsListView = document.getElementById('results-list-view');
        const resultsTableView = document.getElementById('results-table-view');
        const summaryBanner = document.getElementById('summary-banner');
        const legitCountDisplay = document.getElementById('legit-count-display');
        const fakeCountDisplay = document.getElementById('fake-count-display');
        const showLegitBtn = document.getElementById('show-legit');
        const showFakeBtn = document.getElementById('show-fake');
        const showAllBtn = document.getElementById('show-all');
        const toggleViewBtn = document.getElementById('toggle-view-btn');
        const downloadCsvBtn = document.getElementById('downloadCsvBtn');

        // --- Check Definitions ---
        const TOTAL_CHECKS = 3;
        const CHECK_NAMES = [
            "Check 1: Font Block (F1/F2/F3)",
            "Check 2: Metadata Sequence (/Producer (KWSP) -> /CreationDate -> /ModDate)",
            "Check 3: Font Block (Helvetica-Bold)"
        ];
        // Extract short names for table headers
        const CHECK_SHORT_NAMES = CHECK_NAMES.map(name => name.substring(0, name.indexOf(':')));


        // --- State Variables ---
        let legitCount = 0;
        let fakeCount = 0;
        let processedResultsData = []; // Store results data objects
        let isTableView = false; // Track view mode
        let currentFilter = 'all'; // Track active filter


        // --- Event Listeners ---
        checkButton.addEventListener('click', handleCheck);
        showLegitBtn.addEventListener('click', () => { currentFilter = 'legit'; filterResults(); });
        showFakeBtn.addEventListener('click', () => { currentFilter = 'fake'; filterResults(); });
        showAllBtn.addEventListener('click', () => { currentFilter = 'all'; filterResults(); });
        toggleViewBtn.addEventListener('click', toggleView);
        downloadCsvBtn.addEventListener('click', generateAndDownloadCsv);


        // --- Core Logic ---
        function handleCheck() {
            const files = pdfUpload.files;
            // Reset state
            resultsListView.innerHTML = '<p>Results will appear here...</p>'; // Reset view
            resultsTableView.innerHTML = ''; // Clear table view
            resultsTableView.style.display = 'none'; // Hide table view
            resultsListView.style.display = 'block'; // Show list view
            isTableView = false; // Reset to list view
            toggleViewBtn.textContent = 'Switch to Table View';

            summaryBanner.style.display = 'none';
            legitCount = 0;
            fakeCount = 0;
            processedResultsData = []; // Clear previous results data
            currentFilter = 'all'; // Reset filter

            // Disable buttons
            showLegitBtn.disabled = true;
            showFakeBtn.disabled = true;
            showAllBtn.disabled = true;
            toggleViewBtn.disabled = true;
            checkButton.disabled = true;

            downloadCsvBtn.style.display = 'none'; // Hide initially

            if (!files || files.length === 0) {
                resultsListView.innerHTML = '<p>Please select one or more PDF files first.</p>';
                checkButton.disabled = false; // Re-enable check button
                return;
            }

            resultsListView.innerHTML = `<p class="processing-notice">Processing ${files.length} file(s)...</p>`;
            let processedCount = 0;

            Array.from(files).forEach(file => {
                const reader = new FileReader();

                reader.onload = function(event) {
                    let resultData;
                    try {
                        const pdfText = event.target.result;
                        resultData = validatePdfText(pdfText); // Returns result object
                         // Add filename to the result object
                        resultData.fileName = file.name;
                    } catch (e) {
                         console.error("Error processing file content:", file.name, e);
                         resultData = {
                            fileName: file.name, // Include filename even in error
                            isLegit: false,
                            checksPassed: 0,
                            checksFailed: TOTAL_CHECKS, // Use new TOTAL_CHECKS
                            checkResults: CHECK_NAMES.map(name => `${name.split(':')[0]}: <span class='check-failed'>ERROR PROCESSING CONTENT</span>`), // Generic error for all checks
                            error: `Error during validation: ${e.message || 'Unknown processing error'}`
                        };
                    } finally {
                        // Increment counts based on the final resultData
                        if (resultData) {
                            if (resultData.isLegit) { legitCount++; } else { fakeCount++; }
                            processedResultsData.push(resultData); // Store the result object
                        }
                        checkCompletion();
                    }
                };

                reader.onerror = function(event) {
                    console.error("Error reading file:", file.name, event);
                    const resultData = {
                        fileName: file.name, // Include filename
                        isLegit: false,
                        checksPassed: 0,
                        checksFailed: TOTAL_CHECKS, // Use new TOTAL_CHECKS
                        checkResults: CHECK_NAMES.map(name => `${name.split(':')[0]}: <span class='check-failed'>ERROR READING FILE</span>`), // Generic error for all checks
                        error: `Error reading file: ${event.target.error?.message || 'Unknown read error'}`
                    };
                    // Increment fake count for read errors
                    fakeCount++;
                    processedResultsData.push(resultData); // Store the error result object
                    checkCompletion();
                };

                // Use readAsBinaryString for potentially better handling of raw PDF bytes
                reader.readAsBinaryString(file);

                function checkCompletion() {
                    processedCount++;
                    if (processedCount === files.length) {
                         checkButton.disabled = false;
                         updateSummaryBanner();

                         // Enable buttons based on counts
                         const hasResults = processedResultsData.length > 0;
                         showLegitBtn.disabled = legitCount === 0;
                         showFakeBtn.disabled = fakeCount === 0;
                         showAllBtn.disabled = !hasResults;
                         toggleViewBtn.disabled = !hasResults;

                         // Show download button if we have results
                         downloadCsvBtn.style.display = hasResults ? 'inline-block' : 'none';

                         // Initial render (always list view first)
                         renderResultsView();

                         // Remove processing notice from list view if present
                         const notice = resultsListView.querySelector('.processing-notice');
                         if (notice) {
                             resultsListView.removeChild(notice);
                         }
                         if (resultsListView.children.length === 0 && hasResults) {
                             // This case should not happen if renderResultsView worked, but as fallback:
                             resultsListView.innerHTML = '<p>Processing complete. Rendering results failed.</p>';
                         } else if (!hasResults && !notice) { // Only show if notice was also removed
                            resultsListView.innerHTML = '<p>Processing complete. No files processed successfully or no files selected.</p>';
                         }
                    }
                }
            });
        }

        function validatePdfText(pdfText) {
            let checksPassed = 0;
            let checksFailed = 0;
            const checkResults = []; // Store individual results strings (HTML)

            // Normalize line endings
            const normalizedPdfText = pdfText.replace(/\r\n?/g, '\n');

            // --- Check 1 (Formerly Check 2): Specific Font Definition Block ---
            const fontCheckBlock1 = `/Font <<\n/F1 7 0 R\n/F2 8 0 R\n/F3 9 0 R`;
            if (normalizedPdfText.includes(fontCheckBlock1)) {
                 checksPassed++;
                 checkResults.push(`${CHECK_NAMES[0]}: <span class="check-passed">PASSED</span>`); // Use index 0
            } else {
                checksFailed++;
                checkResults.push(`${CHECK_NAMES[0]}: <span class="check-failed">FAILED</span>`); // Use index 0
            }

            // --- Check 2 (Formerly Check 3, now includes Producer check): Metadata Format/Sequence ---
            // UPDATED Regex to specifically require /Producer (KWSP)
            const metadataSequenceRegex = /\/Producer\s*\(KWSP\)\s*\/CreationDate\s*\([^)]*\)\s*\/ModDate\s*\([^)]*\)/;
            if (metadataSequenceRegex.test(normalizedPdfText)) {
                checksPassed++;
                 checkResults.push(`${CHECK_NAMES[1]}: <span class="check-passed">PASSED</span>`); // Use index 1
            } else {
                checksFailed++;
                checkResults.push(`${CHECK_NAMES[1]}: <span class="check-failed">FAILED</span>`); // Use index 1
            }

            // --- Check 3 (Formerly Check 4): Helvetica Font Block ---
            const fontCheckBlock2 = `/Type /Font\n/Subtype /Type1\n/BaseFont /Helvetica-Bold\n/Encoding /WinAnsiEncoding`;
             if (normalizedPdfText.includes(fontCheckBlock2)) {
                checksPassed++;
                 checkResults.push(`${CHECK_NAMES[2]}: <span class="check-passed">PASSED</span>`); // Use index 2
            } else {
                checksFailed++;
                checkResults.push(`${CHECK_NAMES[2]}: <span class="check-failed">FAILED</span>`); // Use index 2
            }

            // Determine overall status: FAKE if 2 or more checks fail (out of 3 total checks)
            const isLegit = (checksFailed < 2); // Legit if 0 or 1 check failed
            // Return object containing all necessary data for rendering
            return { isLegit, checksPassed, checksFailed, checkResults };
        }

        // Add sorting function
        function sortResultsByFilename(results) {
            return [...results].sort((a, b) => a.fileName.localeCompare(b.fileName));
        }

        // Modify renderResultsView function
        function renderResultsView() {
            // Sort the results before rendering
            const sortedResults = sortResultsByFilename(processedResultsData);
            
            // Clear previous content
            resultsListView.innerHTML = '';
            resultsTableView.innerHTML = '';

            if (sortedResults.length === 0) {
                 // Ensure the correct view is displayed even when empty
                 if (isTableView) {
                    resultsTableView.innerHTML = '<p>No results to display.</p>';
                    resultsListView.style.display = 'none';
                    resultsTableView.style.display = 'block';
                 } else {
                    resultsListView.innerHTML = '<p>No results to display.</p>';
                    resultsTableView.style.display = 'none';
                    resultsListView.style.display = 'block';
                 }
                 // Update button text regardless of content
                 toggleViewBtn.textContent = isTableView ? 'Switch to List View' : 'Switch to Table View';
                 return; // Exit early if no data
            }


            if (isTableView) {
                // --- Render Table View ---
                resultsListView.style.display = 'none';
                resultsTableView.style.display = 'block';
                toggleViewBtn.textContent = 'Switch to List View';

                const table = document.createElement('table');
                table.id = 'results-table';
                const thead = table.createTHead();
                const tbody = table.createTBody();
                const headerRow = thead.insertRow();

                // Create headers
                const headers = ['Filename', 'Status', 'Passed', 'Failed', ...CHECK_SHORT_NAMES, 'Details'];
                headers.forEach(text => {
                    const th = document.createElement('th');
                    th.textContent = text;
                    headerRow.appendChild(th);
                });

                // Populate table body with sorted results
                sortedResults.forEach(result => {
                    const row = tbody.insertRow();
                    // Add class for filtering
                    row.classList.add(result.isLegit ? 'result-legit' : 'result-fake');

                    // Filename
                    row.insertCell().textContent = result.fileName;
                    // Status
                    const statusCell = row.insertCell();
                    const statusSpan = document.createElement('span');
                    statusSpan.textContent = result.isLegit ? 'Legit' : 'FAKE';
                    statusSpan.className = result.isLegit ? 'status-legit' : 'status-fake';
                    statusCell.appendChild(statusSpan);
                    // Passed / Failed counts
                    row.insertCell().textContent = result.checksPassed;
                    row.insertCell().textContent = result.checksFailed;

                    // Individual check results
                    result.checkResults.forEach(checkHtml => {
                        const cell = row.insertCell();
                        // Extract simple status (PASSED/FAILED/ERROR) from the HTML
                        if (checkHtml.includes('check-passed')) {
                            cell.innerHTML = '<span class="check-passed">PASSED</span>';
                        } else if (checkHtml.includes('check-failed')) {
                             // Distinguish between regular fail and error messages
                             if (checkHtml.includes('ERROR')) {
                                 cell.innerHTML = '<span class="check-error">ERROR</span>';
                             } else {
                                 cell.innerHTML = '<span class="check-failed">FAILED</span>';
                             }
                        } else {
                            cell.textContent = '?'; // Should not happen
                        }
                    });
                     // Handle cases where result.checkResults might be shorter than TOTAL_CHECKS (e.g., early error)
                     // Pad row if checkResults array is shorter than expected (due to early error?)
                     let currentCheckCells = result.checkResults.length;
                     while(row.cells.length < headers.length -1) { // -1 for the final Details cell
                        row.insertCell().textContent = '-'; // Placeholder for missing check results
                     }

                    // Details (Error message if exists)
                    row.insertCell().textContent = result.error || ''; // Display error or empty string

                });

                resultsTableView.appendChild(table);

            } else {
                // --- Render List View ---
                resultsTableView.style.display = 'none';
                resultsListView.style.display = 'block';
                toggleViewBtn.textContent = 'Switch to Table View';

                // Use sorted results for list view
                sortedResults.forEach(result => {
                    const item = document.createElement('div');
                    item.classList.add('result-item');
                    item.classList.add(result.isLegit ? 'result-legit' : 'result-fake');

                    const statusClass = result.isLegit ? 'status-legit' : 'status-fake';
                    const statusText = result.isLegit ? 'Legit' : 'FAKE';
                    // Note adjusted for 3 checks, Legit if < 2 fail
                    const note = (result.isLegit && result.checksFailed > 0) ? `<span class="note">(Marked 'Legit' as only ${result.checksFailed} check failed)</span>` : '';

                    let summaryHtml = `<span class="summary">(Passed: ${result.checksPassed}, Failed: ${result.checksFailed})</span>`;
                    if (result.error) {
                        // Make error message slightly less prominent in list view summary
                        summaryHtml += ` <span class="status-fake" style="font-weight:normal; font-style:italic;"> Error: ${result.error}</span>`;
                    }

                    // Use the stored HTML check results directly
                    let detailsHtml = result.checkResults.map(res => `<span>${res}</span>`).join('');

                    item.innerHTML = `
                        <div class="result-header">
                            <span class="filename">${result.fileName}:</span>
                            <span class="${statusClass}">${statusText}</span>
                            ${summaryHtml}
                            ${note}
                        </div>
                        <div class="check-details">
                            ${detailsHtml}
                        </div>
                    `;
                    resultsListView.appendChild(item);
                });
            }

            // Re-apply the current filter after rendering
            filterResults();
        }


        // --- Toggle View Function ---
        function toggleView() {
            isTableView = !isTableView; // Flip the state
            renderResultsView(); // Re-render the view
        }

        // --- Filter function to handle both views ---
        function filterResults() {
            let itemsToFilter;
            let viewContainer;

            if (isTableView) {
                 viewContainer = resultsTableView;
                 // Select table rows within the tbody
                 itemsToFilter = viewContainer.querySelectorAll('#results-table tbody tr');
            } else {
                viewContainer = resultsListView;
                // Select list items
                itemsToFilter = viewContainer.querySelectorAll('.result-item');
            }

             if (!itemsToFilter || itemsToFilter.length === 0) {
                 // If the active view is empty after filtering, show a message
                 if (viewContainer.style.display !== 'none') { // Check if the container is supposed to be visible
                     // Avoid adding message if container is hidden (e.g., table view hidden while filtering list)
                     // Check if it only contains a <p> tag already
                     const existingP = viewContainer.querySelector('p');
                     if (!existingP) { // Add message only if no message exists
                        // viewContainer.innerHTML = '<p>No results match the current filter.</p>';
                        // Let's not add a message, just show nothing.
                     }
                 }
                 return;
             }

            // Clear any previous "no results match" message
            const noMatchMsg = viewContainer.querySelector('p');
            if (noMatchMsg && noMatchMsg.textContent.includes('match the current filter')) {
                viewContainer.removeChild(noMatchMsg);
            }


            let hasVisibleItems = false;
            itemsToFilter.forEach(item => {
                let showItem = false;
                switch(currentFilter) { // Use the stored currentFilter state
                    case 'legit':
                        if (item.classList.contains('result-legit')) {
                            showItem = true;
                        }
                        break;
                    case 'fake':
                         if (item.classList.contains('result-fake')) {
                            showItem = true;
                        }
                        break;
                    case 'all':
                    default:
                       showItem = true;
                       break;
                }

                if (showItem) {
                    item.classList.remove('hidden-result');
                    hasVisibleItems = true;
                } else {
                    item.classList.add('hidden-result');
                }
            });

             // Optional: Show message if filter results in no visible items
            // if (!hasVisibleItems && viewContainer.style.display !== 'none') {
            //      const existingP = viewContainer.querySelector('p');
            //      if (!existingP) { // Avoid duplicate messages
            //         const p = document.createElement('p');
            //         p.textContent = 'No results match the current filter.';
            //         p.style.fontStyle = 'italic';
            //         viewContainer.appendChild(p);
            //      }
            // }
        }

        // --- Utility Functions ---
        function updateSummaryBanner() {
            legitCountDisplay.textContent = `Legit: ${legitCount}`;
            fakeCountDisplay.textContent = `Fake: ${fakeCount}`;
            if (legitCount > 0 || fakeCount > 0) {
                summaryBanner.style.display = 'block';
            } else {
                 summaryBanner.style.display = 'none';
            }
        }

        // Modify generateAndDownloadCsv function
        function generateAndDownloadCsv() {
            if (!processedResultsData.length) return;
            
            // Sort the results before generating CSV
            const sortedResults = sortResultsByFilename(processedResultsData);
            
            // Define CSV headers to match the screenshot format
            const headers = ['Filename', 'Status', 'Passed', 'Failed', 'Check 1', 'Check 2', 'Check 3', 'Details'];
            
            // Create CSV content
            let csvContent = headers.join(',') + '\n';
            
            sortedResults.forEach(result => {
                const row = [
                    // Escape filename if it contains commas
                    `"${result.fileName}"`,
                    result.isLegit ? 'Legit' : 'FAKE',
                    result.checksPassed,
                    result.checksFailed,
                    // Extract PASSED/FAILED status from check results HTML
                    result.checkResults[0].includes('check-passed') ? 'PASSED' : 'FAILED',
                    result.checkResults[1].includes('check-passed') ? 'PASSED' : 'FAILED',
                    result.checkResults[2].includes('check-passed') ? 'PASSED' : 'FAILED',
                    result.error ? `"${result.error}"` : '-' // Use dash if no error
                ];
                
                csvContent += row.join(',') + '\n';
            });
            
            // Create blob and download
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'html_epf_validation_results.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

    </script>

</body>
</html>