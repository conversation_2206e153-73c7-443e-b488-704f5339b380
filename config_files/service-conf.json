{"name": "foobar Service", "serviceName": "foobar", "env": "dev", "host": "0.0.0.0", "port": 8080, "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "root:@tcp(localhost:3306)/foobar?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800s"}, "slave": {"dsn": "root:@tcp(localhost:3306)/foobar?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "localhost", "port": 8125}, "trace": {"host": "localhost", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": true}, "redisConfig": {"addr": "localhost:7000", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "", "tlsEnabled": false}, "loanCoreLOCAccountKafkaConfig": {"brokers": ["localhost:9093"], "stream": "dev-loan-core-loc-account", "clusterType": "critical", "enableTLL": false, "offsetType": "oldest", "clientID": "loan-core-local", "packageName": "pb", "dtoName": "LoanCoreLoc"}, "cardInfoStaticPrivateKey": "{{ CARD_INFO_STATIC_PRIVATE_KEY }}", "slackBotURL": "*********************************************************************************", "testCanaryIsError": false}