package retry_attempt

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/dbmy/foobar/server/config"
)

const (
	WorkflowID      = "retry_attempt"
	logRetryAttempt = "retry_attempt"

	stInit = we.StateInit

	stCompleted = we.State(230)
	stFailed    = we.State(240)
)

// making these constants public for the sake of simplicty
const (
	EvNoNeed   = we.EventNoNeed
	EvComplete = we.Event(101)
	EvFail     = we.Event(105)
)

type ExecutionData struct {
	State we.State
}

// GetState ...
func (p *ExecutionData) GetState() we.State {
	return p.State
}

// SetState ...
func (p *ExecutionData) SetState(state we.State) {
	p.State = state
}

// Marshal ...
func (p *ExecutionData) Marshal() ([]byte, error) {
	return json.Marshal(p)
}

// Unmarshal ...
func (p *ExecutionData) Unmarshal(byteData []byte) error {
	return json.Unmarshal(byteData, p)
}

// clone ...
func (p *ExecutionData) clone() *ExecutionData {
	newCtx := *p
	return &newCtx
}

type WorkflowImpl struct {
	AppConfig *config.AppConfig `inject:"config"`
}

func (w *WorkflowImpl) Register() {
	wf := we.NewWorkflow(WorkflowID, func() we.ExecutionData {
		return &ExecutionData{}
	})
	transOpt := &we.TransitionOptions{
		RetryPolicy: &we.RetryPolicy{
			MaxAttempts: 2,
			Interval:    1,
		},
	}
	wf.AddTransitionWithTransCxt(logRetryAttempt, stInit, EvNoNeed, w.complete, transOpt, stCompleted, stFailed)
	we.RegisterWorkflow(wf)
}

// complete is just a state transition function example which normally contains the following operation
// 1. cast the execution data into your workflow specific execution data
// 2. clone the casted execution data
// 3. run your business logic
// 4. move the state by mutating the execution data
// 5. return the execution data
func (w *WorkflowImpl) complete(ctx context.Context, transitionID string, execData we.ExecutionData, transCtx we.TransitionContext, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, fmt.Errorf("invalid execution data passed in complete state")
	}
	attemptTag := slog.CustomTag("current_attempt", transCtx.TotalAttempts+1)
	maxAttempts := slog.CustomTag("max_attempt", transCtx.MaxAttempts)
	ctx = slog.AddTagsToContext(ctx, attemptTag, maxAttempts)

	fmt.Printf("transitioning from %v", currCtx.GetState())
	nextCtx := currCtx.clone()

	var err error

	if num, err2 := randomNumber10(); err2 != nil || num < 9 {
		if err2 != nil {
			slog.FromContext(ctx).Error(logRetryAttempt, "error generating random number", slog.Error(err2))
			err = err2
		} else {
			slog.FromContext(ctx).Warn(logRetryAttempt, fmt.Sprintf("random number %d is less than 9", num))
			err = fmt.Errorf("random number %d is less than 9", num)
		}
	}

	if err != nil {
		if isRetryGoingToExhaust(transCtx) {
			slog.FromContext(ctx).Error(logRetryAttempt, "exhausted all retries", slog.Error(err))
			nextCtx.SetState(stFailed)
		} else {
			return nextCtx, err
		}
	} else {
		nextCtx.SetState(stCompleted)
	}

	fmt.Printf("transitioning to %v", nextCtx.GetState())
	return nextCtx, nil
}

func randomNumber10() (int64, error) {
	// Generate a random number between 0 and 1
	n, err := rand.Int(rand.Reader, big.NewInt(10))
	if err != nil {
		fmt.Println("Error generating random number:", err)
		return 0, err
	}
	return n.Int64(), err
}

func isRetryGoingToExhaust(transCtx we.TransitionContext) bool {
	return transCtx.TotalAttempts+1 > transCtx.MaxAttempts
}

func InitExecution(ctx context.Context, idempotencyKey string) (ExecutionData, error) {
	data := &ExecutionData{}

	if initErr := we.InitExecution(ctx, we.Execution{
		WorkflowID: WorkflowID,
		RequestID:  idempotencyKey,
	}, data); initErr != nil {
		if !errors.Is(initErr, we.ErrExecutionAlreadyExists) {
			slog.FromContext(ctx).Warn(logRetryAttempt, fmt.Sprintf("error when initiating create retry attempt workflow: %s", initErr.Error()))
			return *data, initErr
		}
	}

	execData, err := we.GetExecution(ctx, we.Execution{
		WorkflowID: WorkflowID,
		RequestID:  idempotencyKey,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(logRetryAttempt, fmt.Sprintf("error when getting existing create retry attempt workflow, err: %s", err.Error()))
		return *data, err
	}
	data = execData.(*ExecutionData)
	return *data, nil
}
