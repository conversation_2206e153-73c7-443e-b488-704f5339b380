package create_account

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.myteksi.net/dakota/schemas/streams/apis/loan_core_loc"
	we "gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/dbmy/foobar/dto"
	"gitlab.myteksi.net/dbmy/foobar/kafka/publishers"
	"gitlab.myteksi.net/dbmy/foobar/server/config"
)

const (
	WorkflowID         = "create_account_flow"
	logAccountCreation = "account_creation"

	stInit = we.StateInit

	stCreationProcessing = we.State(210)
	stCreationCompleted  = we.State(230)
)

// making these constants public for the sake of simplicty
const (
	EvNoNeed        = we.EventNoNeed
	EvPersistDB     = we.Event(101)
	EvPersistStream = we.Event(102)
)

type ExecutionData struct {
	State                  we.State
	ProductCode            string
	ProductVariantVersion  string
	CreateAccountStreamMsg *dto.CreateAccountStreamMessage
	ErrorCode              string
	ErrorMsg               string
	IntermittentStatus     string
}

// GetState ...
func (p *ExecutionData) GetState() we.State {
	return p.State
}

// SetState ...
func (p *ExecutionData) SetState(state we.State) {
	p.State = state
}

// Marshal ...
func (p *ExecutionData) Marshal() ([]byte, error) {
	return json.Marshal(p)
}

// Unmarshal ...
func (p *ExecutionData) Unmarshal(byteData []byte) error {
	return json.Unmarshal(byteData, p)
}

// clone ...
func (p *ExecutionData) clone() *ExecutionData {
	newCtx := *p
	return &newCtx
}

type WorkflowImpl struct {
	AppConfig *config.AppConfig    `inject:"config"`
	Publisher publishers.Publisher `inject:"publisher.loc"`
}

// Register ...
/*
In this workflow example, we will demo an async workflow as follows:
1. Setup
- Define the specific workflow states, events and transitions
- Define a publisher that would relay message to a workflow kafka topic for async processing. The publisher is normally
being used by the caller that expects the next state transition to be asynchronous.
- Define a consumer that would consume the workflow kafka topic for async processing.
- Register the workflow, publisher and consumer on app init

2. Run the workflow (sync)
- The sync part is normally triggered by an api call.
- In the api handler, we initialise the workflow by moving the state from "stInit" to "stCreationProcessing" with
an event identifier "EvPersistDB" and a transition function "w.persistDB".
- Then we will publish a message which would embed the workflow instance identifier (requestID or transitionID). You must
find a way to pass these identifiers into the kafka message in order to continue the workflow from another go-routine.
- After that we'll normally return an ack response to the client to signify the request is accepted and processing.
- The client would normally need to get the ultimate response by other mean such as a kafka response topic or polling another api endpoint.

3. Continue the workflow (async)
- The consumer would consume the workflow kafka message, extract the workflow instance identifier (requestID or transitionID)
and move the state from "stCreationProcessing" to "stCreationCompleted" with an event identifier "EvPersistStream" and a transition function "w.persistDB".
*/
func (w *WorkflowImpl) Register() {
	wf := we.NewWorkflow(WorkflowID, func() we.ExecutionData {
		return &ExecutionData{}
	})
	wf.AddTransition(logAccountCreation, stInit, EvPersistDB, w.persistDB, nil, stCreationProcessing)
	wf.AddTransition(logAccountCreation, stCreationProcessing, EvPersistStream, w.persistDB, nil, stCreationCompleted)
	we.RegisterWorkflow(wf)
}

// persistDB is just a state transition function example which normally contains the following operation
// 1. cast the execution data into your workflow specific execution data
// 2. clone the casted execution data
// 3. run your business logic
// 4. move the state by mutating the execution data
// 5. return the execution data
func (w *WorkflowImpl) persistDB(ctx context.Context, transitionID string, execData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, fmt.Errorf("invalid execution data passed in persistDB state")
	}

	fmt.Printf("transitioning from %v", currCtx.GetState())

	nextCtx := currCtx.clone()
	// we'll use the stream message event ID to
	if resBytes, err := currCtx.Marshal(); err != nil {
		fmt.Printf("Failed to marshal execution data: %s\n", err.Error())
	} else {
		fmt.Println(string(resBytes))
	}
	if currCtx.GetState() == stInit {
		nextCtx.CreateAccountStreamMsg.EventID = transitionID
		locEvent := &loan_core_loc.LoanCoreLoc{
			ReferenceID: transitionID,
		}
		w.Publisher.Publish(ctx, locEvent)
		nextCtx.SetState(stCreationProcessing)
	} else {
		nextCtx.SetState(stCreationCompleted)
	}

	fmt.Printf("transitioning to %v", nextCtx.GetState())
	return nextCtx, nil
}
