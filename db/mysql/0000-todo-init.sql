CREATE TABLE `todo`
(
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `author` VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'author',
    `description` VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'description',
    `done` TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'done',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    <PERSON><PERSON>Y `index_created_at` (`created_at`),
    <PERSON><PERSON><PERSON> `index_updated_at` (`updated_at`)

) DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;