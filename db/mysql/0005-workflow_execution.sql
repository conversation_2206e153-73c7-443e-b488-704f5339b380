-- Deploy loan-core:0005-workflow_execution to mysql

BEGIN;

CREATE TABLE `workflow_execution`
(
    `id`            BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `workflow_id`   VARCHAR(36)  NOT NULL DEFAULT '' COMMENT 'Identifier of the workflow',
    `run_id`        VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'ID per workflow execution',
    `transition_id` CHAR(36)     NOT NULL DEFAULT '' COMMENT 'ID of current state transition',
    `prev_trans_id` CHAR(36)              DEFAULT '' COMMENT 'ID of previous state transition',
    `attempt`       INT          NOT NULL DEFAULT 0 COMMENT 'Attempt count per transition',
    `state`         INT          NOT NULL COMMENT 'state of the workflow',
    `data`          JSON         NOT NULL COMMENT 'data required for running workflow',
    `created_at`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON>AR<PERSON> KEY (`id`),
    UNIQUE KEY `uk_run_id_workflow_id` (`run_id`, `workflow_id`) COMMENT 'Identifier for a specific execution',
    UNIQUE KEY `uk_transition_id` (`transition_id`) COMMENT 'Identifier for a transition within an execution',
    UNIQUE KEY `uk_prev_trans_id` (`prev_trans_id`) COMMENT 'Identifier for continuing an existing execution',
    INDEX           `index_state_updated_at` (`workflow_id`, `state`, `updated_at`) COMMENT 'For worker to retry failed transitions',
    KEY             `index_created_at` (`created_at`),
    KEY             `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

COMMIT;
